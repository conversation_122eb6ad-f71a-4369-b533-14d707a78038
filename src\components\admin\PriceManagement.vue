<template>
  <div>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="沒有更多了"
      @load="onLoad"
    >
      <van-card
        v-for="price in list"
        :key="price.id"
        :title="price.route_name"
        :desc="`服務: ${price.service_type}`"
      >
        <template #price>
          <span style="font-size: 16px; color: #ee0a24;">TWD {{ price.price }}</span>
        </template>
        <template #tags>
          <van-tag plain type="primary">{{ price.car_type }}</van-tag>
        </template>
      </van-card>
    </van-list>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { List as VanList, Card as VanCard, Tag as VanTag, Notify } from 'vant';

const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const onLoad = async () => {
  if (loading.value || finished.value) return;

  loading.value = true;
  try {
    // 這裡的 API URL 應根據您的後端服務進行調整
    const response = await axios.get('/api/vendor/2/prices');
    
    if (response.data && response.data.length > 0) {
      list.value.push(...response.data);
    } else {
      finished.value = true;
    }
  } catch (error) {
    console.error('Failed to fetch prices:', error);
    Notify({ type: 'danger', message: '讀取價格失敗' });
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // onLoad();
});
</script>

<style scoped>
.van-card {
  margin-bottom: 10px;
  background-color: #fff;
}
</style>