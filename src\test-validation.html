<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表單驗證測試</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vant@4/lib/vant.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vant@4/lib/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 10px 0;
        }
        .info-text {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h2>表單驗證測試</h2>
            <div class="info-text">
                點擊「測試表單驗證」按鈕，然後直接點擊「提交訂單」來測試驗證功能。<br>
                應該會看到錯誤提示和欄位標紅。
            </div>
            
            <van-button 
                type="primary" 
                class="test-button"
                @click="showPopup = true"
            >
                測試表單驗證
            </van-button>

            <!-- 使用 ReservationInfoPopup 組件進行測試 -->
            <van-popup
                v-model:show="showPopup"
                position="bottom"
                closeable
                :style="{ height: 'auto', maxHeight: '70%', 'background-color': 'khaki' }"
                round
            >
                <div style="padding: 16px; padding-top: 40px;">
                    <h3>預約資訊確認</h3>
                    
                    <van-cell-group inset>
                        <van-field
                            v-model="appointmentDate"
                            readonly
                            is-link
                            label="日期時間"
                            placeholder="請選擇預約日期時間"
                            required
                            :error="errors.appointmentDate"
                            error-message="請選擇預約日期時間"
                        />
                        
                        <van-field
                            v-model="passengerName"
                            label="訂車人大名"
                            placeholder="請輸入訂車人姓名"
                            required
                            :error="errors.passengerName"
                            error-message="請輸入訂車人大名"
                            @focus="clearError('passengerName')"
                        />
                        
                        <van-field
                            v-model="contactPhone"
                            label="連絡電話"
                            type="tel"
                            placeholder="請輸入連絡電話"
                            required
                            :error="errors.contactPhone"
                            error-message="請輸入有效的連絡電話"
                            @focus="clearError('contactPhone')"
                        />
                        
                        <van-field
                            v-model="pickupAddress"
                            label="上車地址"
                            placeholder="請輸入詳細地址"
                            required
                            :error="errors.pickupAddress"
                            error-message="請輸入詳細地址"
                            @focus="clearError('pickupAddress')"
                        />
                    </van-cell-group>
                    
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <van-button 
                            type="default" 
                            size="large" 
                            style="flex: 1;"
                            @click="showPopup = false"
                        >
                            取消
                        </van-button>
                        <van-button 
                            type="primary" 
                            size="large" 
                            style="flex: 1;"
                            @click="validateForm"
                        >
                            提交訂單
                        </van-button>
                    </div>
                </div>
            </van-popup>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { Toast } = vant;

        createApp({
            setup() {
                const showPopup = ref(false);
                const appointmentDate = ref('');
                const passengerName = ref('');
                const contactPhone = ref('');
                const pickupAddress = ref('');
                
                const errors = ref({
                    appointmentDate: false,
                    passengerName: false,
                    contactPhone: false,
                    pickupAddress: false
                });

                const clearError = (fieldName) => {
                    errors.value[fieldName] = false;
                };

                const clearAllErrors = () => {
                    Object.keys(errors.value).forEach(key => {
                        errors.value[key] = false;
                    });
                };

                const validateForm = () => {
                    clearAllErrors();
                    let hasError = false;

                    if (!appointmentDate.value || appointmentDate.value.trim() === '') {
                        errors.value.appointmentDate = true;
                        Toast({ type: 'fail', message: '請選擇預約日期時間！' });
                        hasError = true;
                    }

                    if (!passengerName.value || passengerName.value.trim() === '') {
                        errors.value.passengerName = true;
                        Toast({ type: 'fail', message: '請輸入訂車人大名！' });
                        hasError = true;
                    }

                    if (!contactPhone.value || contactPhone.value.trim() === '') {
                        errors.value.contactPhone = true;
                        Toast({ type: 'fail', message: '請輸入連絡電話！' });
                        hasError = true;
                    } else {
                        const phoneRegex = /^[0-9+\-\s()]{8,15}$/;
                        if (!phoneRegex.test(contactPhone.value.trim())) {
                            errors.value.contactPhone = true;
                            Toast({ type: 'fail', message: '請輸入有效的連絡電話！' });
                            hasError = true;
                        }
                    }

                    if (!pickupAddress.value || pickupAddress.value.trim() === '') {
                        errors.value.pickupAddress = true;
                        Toast({ type: 'fail', message: '請輸入詳細地址！' });
                        hasError = true;
                    }

                    if (!hasError) {
                        Toast({ type: 'success', message: '表單驗證通過！' });
                        showPopup.value = false;
                    }
                };

                return {
                    showPopup,
                    appointmentDate,
                    passengerName,
                    contactPhone,
                    pickupAddress,
                    errors,
                    clearError,
                    validateForm
                };
            }
        }).use(vant).mount('#app');
    </script>
</body>
</html>
