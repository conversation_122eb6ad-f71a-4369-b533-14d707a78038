
<template>
  <!-- <Navbar :title="'報價查詢'" :background-color="'#1976d2'" /> -->
  <div class="car-quota-container">
    <div class="order-header">
      <!-- <h1>Car Quota</h1>
      <p class="subtitle">請填寫以下資訊以獲取報價</p> -->
    </div>

    <van-tabs v-model:active="active" type="card" class="custom-tabs">
      <van-tab title="接送機場">
        <AirportTransfer :vendor-id="vendorId" />
      </van-tab>
      <van-tab title="包車旅遊">
        <CarTour :vendor-id="vendorId" />
      </van-tab>
      <van-tab title="兩地接送">
        <Reservation :vendor-id="vendorId" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import AirportTransfer from './quota/AirportTransfer.vue';
import CarTour from './quota/CarTour.vue';
import Reservation from './quota/Reservation.vue';
import Navbar from './Navbar.vue';

// 接收 props
const props = defineProps({
  vendor_id: {
    type: [String, Number],
    default: ''
  }
});

// 獲取路由參數
const route = useRoute();

// 計算 vendor_id，優先使用 URL 參數，其次使用 props
const vendorId = computed(() => {
  // 優先使用路由參數
  if (route.params.vendor_id) {
    return route.params.vendor_id;
  }
  // 其次使用 props
  if (props.vendor_id) {
    return props.vendor_id;
  }
  // 默認值
  return '2';
});

// 定義響應式狀態
const active = ref(Number(localStorage.getItem('carQuotaTabActive') || 0));

watch(active, (val) => {
  localStorage.setItem('carQuotaTabActive', val);
});

// 組件掛載時輸出 vendor_id 用於調試
onMounted(() => {
  console.log('CarQuota 組件掛載，vendor_id:', vendorId.value);
});
</script>

<style scoped>
.car-quota-container {
  padding: 12px;
  background-color: #f5f7fa;
  min-height: 100vh;
  max-width: 600px;
  margin: 0 auto;
}

.custom-tabs {
  margin-bottom: 12px;
}

.custom-tabs :deep(.van-tab) {
  color: #666;
  font-size: 18px;
}

.custom-tabs :deep(.van-tab--active) {
  color: #1989fa;
  font-weight: 500;
  font-size: 18px;
}

.custom-tabs :deep(.van-tabs__nav--card) {
  border-color: #e0e0e0;
}

.custom-tabs :deep(.van-tabs__nav--card .van-tab.van-tab--active) {
  background-color: #1989fa;
  border-color: #1989fa;
  color: white;
}

.order-header {
  text-align: center;
  margin-bottom: 12px;
}

.order-header h1 {
  font-size: 20px;
  color: #333;
  margin-bottom: 4px;
}

.subtitle {
  color: #666;
  font-size: 13px;
  margin-top: 0;
}

.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #1989fa;
  margin: 4px 16px;
  padding-bottom: 4px;
  border-bottom: 1px solid #ebedf0;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.custom-cell {
  padding: 16px;
  border-bottom: 1px solid #f2f2f2;
}

.custom-cell:last-child {
  border-bottom: none;
}

.custom-cell :deep(.van-cell__title) {
  color: #333 !important;
  font-size: 16px;
  font-weight: 500;
  flex: 0 0 100px;
}

.required-field::before {
  content: '*';
  color: #ee0a24;
  margin-right: 4px;
}

.location-cell :deep(.van-cell__value) {
  color: #1989fa;
}

.custom-icon {
  color: #1989fa;
  font-size: 16px;
}

.custom-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.custom-radio {
  margin-right: 16px;
  margin-bottom: 8px;
}

.custom-radio :deep(.van-radio__label) {
  color: #333;
}

.custom-radio :deep(.van-radio__icon--checked .van-icon) {
  background-color: #1989fa;
  border-color: #1989fa;
}

.checkbox-cell :deep(.van-cell__value) {
  align-items: flex-start;
}

.checkbox-container {
  width: 100%;
}

.custom-checkbox-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.custom-checkbox {
  margin-bottom: 12px;
  width: 100%;
}

.custom-checkbox:last-child {
  margin-bottom: 0;
}

.custom-checkbox :deep(.van-checkbox__label) {
  color: #333;
  font-size: 14px;
}

.custom-checkbox :deep(.van-checkbox__icon--checked .van-icon) {
  background-color: #1989fa;
  border-color: #1989fa;
}

.custom-cell :deep(.van-cell__value) {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.compact-container {
  padding: 4px 0;
}

.compact-row {
  display: flex;
  padding: 4px 16px;
  border-bottom: 1px solid #f2f2f2;
}

.compact-row.no-border {
  border-bottom: none;
  padding-bottom: 2px;
}

.compact-row.no-padding-top {
  padding-top: 2px;
}

.compact-row:last-child {
  border-bottom: none;
}

.inline-cell {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
}

.inline-cell:first-child {
  padding-left: 0;
}

.inline-cell:last-child {
  padding-right: 0;
}

.inline-label {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-bottom: 0;
}

.compact-stepper {
  width: 75px;
  margin-left: 6px;
}

.compact-stepper :deep(.van-stepper__minus),
.compact-stepper :deep(.van-stepper__plus) {
  background-color: #1989fa;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 10px;
}

.compact-stepper :deep(.van-stepper__input) {
  background-color: #f5f7fa;
  height: 20px;
  width: 24px;
  font-size: 13px;
  margin: 0 4px;
}

.custom-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  width: 100%;
}

.custom-textarea :deep(.van-field__control) {
  min-height: 60px;
  font-size: 14px;
  padding: 8px;
}

.submit-area {
  margin-top: 16px;
  padding: 0 8px 16px;
}

.submit-button {
  background-color: #1989fa;
  border: none;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
  transition: all 0.3s ease;
}

.submit-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.3);
}
</style>
