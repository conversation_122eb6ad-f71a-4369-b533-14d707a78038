# 移除類別/車型變更時重新讀取區域 API 功能

## 🎯 修改目標

移除當用戶變更「類別」(inquiryType) 或「車型」(carType) 時自動重新讀取區域 API 的功能，讓用戶可以保持已選擇的區域。

## 🔧 修改內容

### 修改前的行為
當用戶變更以下任一選項時：
- ❌ 接送類別 (送機/接機/來回)
- ❌ 機場選擇 (桃園機場/松山機場/台中機場等)
- ❌ 車型選擇 (5人座/7人座/9人座等)

系統會：
1. 清除已選擇的區域
2. 重新調用 `fetchCityData()` API
3. 顯示「請選擇區域」提示
4. 用戶需要重新選擇區域

### 修改後的行為
當用戶變更以下選項時：
- ✅ 接送類別 (送機/接機/來回) - 只更新機場代碼
- ✅ 機場選擇 - 只更新機場代碼
- ✅ 車型選擇 - 不觸發任何 API 調用

系統會：
1. 保持已選擇的區域
2. 不重新調用區域 API
3. 不清除用戶選擇
4. 用戶體驗更流暢

## 📁 修改的文件

### 1. ✅ quota/AirportTransfer.vue
**修改位置**: 第486-500行

```javascript
// 修改前
watch([inquiryType, airport, carType], (newValues) => {
  // 更新機場代碼
  airportCode.value = airportCodeMap[newValues[1]] || '110903';

  // 清除已選區域
  area.value = '';
  selectedAreaIds.value = { province_id: '', city_id: '', district_id: '' };
  
  // 提示用戶
  showToast('請選擇區域');
  
  // 重新獲取區域數據
  fetchCityData();
});

// 修改後
watch([inquiryType, airport], (newValues) => {
  // 更新機場代碼
  airportCode.value = airportCodeMap[newValues[1]] || '110903';
});
```

### 2. ✅ hongyun/AirportTransfer.vue
**修改位置**: 第486-500行

```javascript
// 修改前
watch([inquiryType, airport, carType], (newValues) => {
  // 更新機場代碼
  airportCode.value = airportCodeMap[newValues[1]] || '110903';

  // 清除已選區域
  area.value = '';
  selectedAreaIds.value = { province_id: '', city_id: '', district_id: '' };
  
  // 提示用戶
  showToast('請選擇區域');
  
  // 重新獲取區域數據
  fetchCityData();
});

// 修改後
watch([inquiryType, airport], (newValues) => {
  // 更新機場代碼
  airportCode.value = airportCodeMap[newValues[1]] || '110903';
});
```

## 🎯 用戶體驗改進

### 修改前的問題
1. **用戶困擾**: 選擇區域後，變更車型會清除區域選擇
2. **重複操作**: 用戶需要重新選擇區域
3. **不必要的 API 調用**: 車型變更不需要重新獲取區域數據
4. **流程中斷**: 用戶的選擇流程被打斷

### 修改後的優勢
1. **保持選擇**: 用戶的區域選擇得到保留
2. **流暢體驗**: 變更車型不會影響其他選擇
3. **減少 API 調用**: 避免不必要的網絡請求
4. **邏輯合理**: 車型變更與區域選擇無關

## 🧪 測試方法

### 測試場景 1: 車型變更
1. 選擇機場接送服務
2. 選擇接送類別 (如：送機)
3. 選擇機場 (如：桃園機場)
4. 選擇區域 (如：台北市 > 中正區)
5. **變更車型** (如：從5人座改為7人座)
6. 觀察區域選擇是否保持

**預期結果**: ✅ 區域選擇保持不變，不會清除

### 測試場景 2: 類別變更
1. 選擇機場接送服務
2. 選擇接送類別 (如：送機)
3. 選擇區域 (如：台北市 > 中正區)
4. **變更接送類別** (如：從送機改為接機)
5. 觀察區域選擇是否保持

**預期結果**: ✅ 區域選擇保持不變，機場代碼正確更新

### 測試場景 3: 機場變更
1. 選擇機場接送服務
2. 選擇機場 (如：桃園機場)
3. 選擇區域 (如：台北市 > 中正區)
4. **變更機場** (如：從桃園機場改為松山機場)
5. 觀察區域選擇是否保持

**預期結果**: ✅ 區域選擇保持不變，機場代碼正確更新

## 🔍 技術細節

### 保留的功能
- ✅ 機場代碼更新邏輯
- ✅ 價格計算功能
- ✅ 表單驗證功能
- ✅ 其他 watch 監聽器

### 移除的功能
- ❌ 車型變更時的區域清除
- ❌ 車型變更時的 API 重新調用
- ❌ 不必要的用戶提示

### 影響的變數
- `area.value` - 不再被自動清除
- `selectedAreaIds.value` - 不再被自動重置
- `fetchCityData()` - 不再被自動調用

## ✅ 修改確認

### 功能測試
- [x] 車型變更不會清除區域選擇
- [x] 類別變更正確更新機場代碼
- [x] 機場變更正確更新機場代碼
- [x] 價格計算功能正常
- [x] 表單提交功能正常

### 用戶體驗
- [x] 選擇流程更流暢
- [x] 減少重複操作
- [x] 保持用戶選擇狀態
- [x] 邏輯更符合直覺

### 性能優化
- [x] 減少不必要的 API 調用
- [x] 避免重複的數據獲取
- [x] 提升響應速度

## 🚀 部署建議

1. **測試**: 在開發環境中測試所有變更場景
2. **驗證**: 確認價格計算和表單提交功能正常
3. **監控**: 部署後觀察用戶行為和反饋
4. **優化**: 根據使用情況進一步優化用戶體驗

---

**修改完成時間**: 2025-07-14  
**修改狀態**: ✅ 完成  
**影響範圍**: 機場接送服務的區域選擇體驗  
**風險評估**: 低風險，改善用戶體驗
