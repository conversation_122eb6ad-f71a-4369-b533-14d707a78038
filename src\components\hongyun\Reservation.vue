<template>
  <div class="reservation">
    <div class="form-section">
      <div class="add-stop-btn-row">
        <van-button type="primary" size="small" class="add-stop-btn" @click="addStopAddress">
          <van-icon name="plus" /> 新增停靠點
        </van-button>
      </div>
      <van-cell-group inset>
        <draggable
          v-model="allAddresses"
          handle=".drag-handle"
          item-key="id"
          :animation="200"
          class="draggable-list"
        >
          <template #item="{ element, index }">
            <van-field
              v-model="element.value"
              clearable
              :placeholder="element.placeholder"
              class="custom-field drag-field"
            >
              <template #label>
                <div class="address-label">
                  <van-icon :name="element.icon" class="address-icon" />
                  <span>{{ element.label }}</span>
                </div>
              </template>
              <template #right-icon>
                <div class="stop-actions">
                  <van-icon name="wap-nav" class="drag-handle" />
                  <van-icon name="cross" @click="removeAddress(index)" v-if="element.type === 'stop' || (element.type !== 'pickup' && element.type !== 'dropoff')" />
                </div>
              </template>
            </van-field>
          </template>
        </draggable>
      </van-cell-group>
    </div>

    <div class="submit-area">
      <van-button type="primary" block round size="large" class="submit-button" @click="getEstimate">
        取得估價
      </van-button>
    </div>

    <!-- 使用可重複使用的預約資訊彈窗 -->
    <ReservationInfoPopup
      v-model:show="showReservationPopup"
      :total-price="selectedCarPrice"
      @confirm="confirmReservationOrder"
      :confirm-loading="isSubmittingReservation"
      v-model:appointmentDateModel="appointmentDate"
      v-model:passengerNameModel="passengerName"
      v-model:passengerCountModel="passengerCount"
      v-model:luggageCountModel="luggageCount"
      v-model:contactPhoneModel="contactPhone"
      v-model:pickupAddressModel="detailedPickupAddressForPopup"
      v-model:payTypeModel="payType"
      :inquiryType="'兩地接送'"
      title="預約資訊確認"
    />


    <!-- API 結果顯示區域 (移到主 div 內部) -->
    <div v-if="estimateResult && !estimateResult.error" class="form-section result-section">
      <div class="section-title">估價結果</div>
      
      <van-grid direction="horizontal" :column-num="2" :border="true" :gutter="8" class="price-grid">
        <van-grid-item class="price-grid-item">
          <div class="grid-item-content">
            <van-icon name="car-outline" class="grid-item-icon"/>
            <div class="car-type-label">5人座</div>
            <div class="price-label">NT$ {{ estimateResult.data && estimateResult.data.estimated_cost_standard !== undefined ? estimateResult.data.estimated_cost_standard : 'N/A' }}</div>
            <van-button 
              v-if="estimateResult.data && estimateResult.data.estimated_cost_standard !== undefined"
              type="primary" 
              size="mini" 
              class="transfer-button"
              @click="transferToOrder('standard', estimateResult.data.estimated_cost_standard)">
              選擇此車型
            </van-button>
          </div>
        </van-grid-item>
        <van-grid-item class="price-grid-item">
          <div class="grid-item-content">
            <van-icon name="van-outline" class="grid-item-icon"/>
            <div class="car-type-label">9人座</div>
            <div class="price-label">NT$ {{ estimateResult.data && estimateResult.data.estimated_cost_van !== undefined ? estimateResult.data.estimated_cost_van : 'N/A' }}</div>
            <van-button 
              v-if="estimateResult.data && estimateResult.data.estimated_cost_van !== undefined"
              type="primary" 
              size="mini" 
              class="transfer-button"
              @click="transferToOrder('van', estimateResult.data.estimated_cost_van)">
              選擇此車型
            </van-button>
          </div>
        </van-grid-item>
      </van-grid>

      <!-- 總距離和總時間 -->
      <div v-if="estimateResult.data && (estimateResult.data.total_distance_km !== undefined || estimateResult.data.total_duration_text)" class="route-summary">
        <van-cell-group inset class="summary-details-group">
          <van-cell 
            v-if="estimateResult.data.total_distance_km !== undefined" 
            title="總距離" 
            :value="`${Math.round(estimateResult.data.total_distance_km * 10) / 10} 公里`" 
            class="summary-cell" 
          />
          <van-cell v-if="estimateResult.data.total_duration_text" title="總時間" :value="estimateResult.data.total_duration_text" class="summary-cell"/>
        </van-cell-group>
      </div>

      <!-- 路徑過程 (legs) -->
            <!-- 路徑過程 (legs) -->
            <div v-if="estimateResult.data && estimateResult.data.legs && estimateResult.data.legs.length > 0" class="route-legs">
        <div class="section-title legs-title">路徑詳情</div>
        <div class="legs-list-container">
          <van-cell-group
            v-for="(leg, index) in estimateResult.data.legs"
            :key="index"
            inset
            class="leg-details-group"
          >
            <van-cell :title="`路段 ${index + 1}`" class="leg-header-cell"/>
        
        <!-- 起點的 CELL -->
        <van-cell class="leg-detail-cell address-cell combined-address-cell">
          <template #title>
            <span class="address-leg-title">起點</span>
          </template>
          <template #label>
            <span class="address-leg-value">{{ leg.origin }}</span>
          </template>
        </van-cell>

        <!-- 終點的 CELL -->
        <van-cell class="leg-detail-cell address-cell combined-address-cell">
          <template #title>
            <span class="address-leg-title">終點</span>
          </template>
          <template #label>
            <span class="address-leg-value">{{ leg.destination }}</span>
          </template>
        </van-cell>

        <!-- 距離/耗時的 CELL (已修正重複問題) -->
        <van-cell v-if="leg.distance_text || leg.duration_text" class="leg-detail-cell distance-duration-cell combined-info-cell">
              <template #title>
                <span v-if="leg.distance_text" class="info-leg-title">路段距離</span>
                <span v-if="leg.distance_text && leg.duration_text" class="info-title-separator">&nbsp;/&nbsp;</span>
                <span v-if="leg.duration_text" class="info-leg-title">路段耗時</span>
              </template>
              <template #value>
                <span v-if="leg.distance_text" class="info-leg-value">{{ leg.distance_text }}</span>
                <span v-if="leg.distance_text && leg.duration_text" class="info-value-separator">&nbsp;/&nbsp;</span>
                <span v-if="leg.duration_text" class="info-leg-value">{{ leg.duration_text }}</span>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </div>

    </div> <!-- 這個 div 結束的是 class="form-section result-section" 且 v-if="estimateResult && !estimateResult.error" -->
    <!-- 顯示 API 錯誤或其他非預期回應 -->
    <div v-if="estimateResult && estimateResult.error" class="form-section result-section">
      <div class="section-title">估價錯誤</div>
      <van-cell-group inset>
        <van-cell title="錯誤訊息" class="custom-cell">
          <template #value>
            <pre class="api-response-pre">{{ JSON.stringify(estimateResult, null, 2) }}</pre>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div> <!-- 這個 div 結束的是 class="reservation" 的主 div -->
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import draggable from 'vuedraggable';
import { showToast, showLoadingToast, closeToast } from 'vant'; // Import Vant utilities
import ReservationInfoPopup from '../shared/ReservationInfoPopup.vue'; // 引入新元件
import { useRouter } from 'vue-router'; // Added useRouter

const router = useRouter(); // Init router

const API_BASE_URL = import.meta.env.VITE_API_SERVER;

const props = defineProps({
  vendorId: {
    type: [String, Number],
    default: '2'
  }
});

const pickupAddress = ref('台北車站'); // 預設起點
const dropoffAddress = ref('桃園機場'); // 預設終點
const stopAddresses = ref([]);

// 新增彈窗及預約相關狀態
const showReservationPopup = ref(false);
const isSubmittingReservation = ref(false);
const appointmentDate = ref('');
const passengerName = ref('');
const passengerCount = ref(1);
const luggageCount = ref(0);
const contactPhone = ref('');
const payType = ref('現金'); // 預設現金
const selectedCarType = ref(''); // 用於儲存用戶選擇的車型 ('standard' or 'van')
const selectedCarPrice = ref(0); // 用於儲存用戶選擇車型的價格
const detailedPickupAddressForPopup = ref(''); // For editable pickup address in popup

const allAddresses = ref([
  { id: 'pickup', type: 'pickup', value: pickupAddress.value, label: '起點:', icon: 'arrow-up', placeholder: '請輸入起點' },
  { id: 'dropoff', type: 'dropoff', value: dropoffAddress.value, label: '終點:', icon: 'flag-o', placeholder: '請輸入終點' },
]);

watch(pickupAddress, val => {
  const idx = allAddresses.value.findIndex(a => a.type === 'pickup');
  if (idx !== -1) allAddresses.value[idx].value = val;
});
watch(dropoffAddress, val => {
  const idx = allAddresses.value.findIndex(a => a.type === 'dropoff');
  if (idx !== -1) allAddresses.value[idx].value = val;
});
watch(
  () => allAddresses.value.map(a => a.value),
  (vals) => {
    // 動態更新標籤
    allAddresses.value.forEach((a, i) => {
      if (i === 0) {
        a.type = 'pickup';
        a.label = '起點:';
        a.icon = 'arrow-up'; // 換成上車icon
        a.placeholder = '台北市忠孝東路五段100號';
      } else if (i === allAddresses.value.length - 1) {
        a.type = 'dropoff';
        a.label = '終點:';
        a.icon = 'flag-o'; // 換成終點旗幟icon
        a.placeholder = '桃園機場';
      } else {
        a.type = 'stop';
        a.label = `停靠點${i}:`;
        a.icon = 'location';
        a.placeholder = `第${i}停靠點地點`;
      }
    });
    // 同步資料
    const pickup = allAddresses.value[0];
    const dropoff = allAddresses.value[allAddresses.value.length - 1];
    if (pickup) pickupAddress.value = pickup.value;
    if (dropoff) dropoffAddress.value = dropoff.value;
    stopAddresses.value = allAddresses.value.slice(1, allAddresses.value.length - 1).map(a => a.value);
  },
  { deep: true }
);

function addStopAddress() {
  allAddresses.value.splice(
    allAddresses.value.length - 1,
    0,
    {
      id: 'stop-' + Date.now() + '-' + Math.random(),
      type: 'stop',
      value: '',
      label: '', // 會由 watch 動態補上
      icon: 'location',
      placeholder: ''
    }
  );
}
function removeAddress(idx) {
  // 僅允許刪除停靠點
  if (idx === 0 || idx === allAddresses.value.length - 1) return;
  allAddresses.value.splice(idx, 1);
}

onMounted(() => {
  // 初始化同步
  pickupAddress.value = allAddresses.value.find(a => a.type === 'pickup').value;
  dropoffAddress.value = allAddresses.value.find(a => a.type === 'dropoff').value;
  console.log('Reservation 組件掛載，vendor_id:', props.vendorId);
});

const estimateResult = ref(null);

const getEstimate = async () => {
  estimateResult.value = null; // Reset previous result

  const locations = allAddresses.value
    .map(address => address.value.trim())
    .filter(addressValue => addressValue !== ''); // Filter out empty strings

  if (locations.length < 2) {
    showToast('請至少輸入起點和終點');
    return;
  }
  
  // Check if any of the required start/end points are empty if they were not filtered out (e.g. just spaces)
  // The filter above should handle empty strings, but this is an additional check for safety.
  if (!allAddresses.value[0].value.trim() || !allAddresses.value[allAddresses.value.length -1].value.trim()){
      showToast('起點和終點地址不能為空');
      return;
  }

  const apiBody = {
    site: "car-quotation",
    locations: locations
  };

  showLoadingToast({
    message: '估價中...',
    forbidClick: true,
    duration: 0
  });

  try {
    const apiUrl = `${API_BASE_URL}/api/getmultipointroutedetails`;

    console.log(`Using API URL: ${apiUrl}`);
    console.log('Sending to API:', JSON.stringify(apiBody));
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(apiBody)
    });

    closeToast();
    const result = await response.json();
    console.log('API Response:', result);

    if (response.ok) { // Check if response status is 2xx
      estimateResult.value = result;
  console.log('Full estimateResult for template:', JSON.parse(JSON.stringify(estimateResult.value)));
  if (estimateResult.value.data && estimateResult.value.data.legs) {
    console.log('Legs array:', JSON.parse(JSON.stringify(estimateResult.value.data.legs)));
    console.log('Number of legs:', estimateResult.value.data.legs.length);
  } else {
    console.log('Legs data is missing or not an array.');
  }
    } else {
      // Handle non-2xx HTTP responses (API errors)
      showToast(result.message || `估價失敗 (${response.status})`);
      estimateResult.value = { error: true, status: response.status, data: result };
    }
  } catch (error) {
    closeToast();
    console.error('Error fetching estimate:', error);
    showToast('估價請求失敗，請檢查網絡或稍後再試');
    estimateResult.value = { error: true, message: error.message };
  }
};

const resetReservationForm = () => {
  // Reset address fields
  pickupAddress.value = '台北車站'; // Or your default
  dropoffAddress.value = '桃園機場'; // Or your default
  allAddresses.value = [
    { id: 'pickup', type: 'pickup', value: pickupAddress.value, label: '起點:', icon: 'arrow-up', placeholder: '請輸入起點' },
    { id: 'dropoff', type: 'dropoff', value: dropoffAddress.value, label: '終點:', icon: 'flag-o', placeholder: '請輸入終點' },
  ];
  stopAddresses.value = [];
  estimateResult.value = null;
  // Reset popup related fields
  appointmentDate.value = '';
  passengerName.value = '';
  contactPhone.value = '';
  payType.value = '現金';
  detailedPickupAddressForPopup.value = '';
};
// 計算屬性，用於在彈窗中顯示主要上車地址 (通常是起點)
const pickupDisplayAddress = computed(() => {
  const pickup = allAddresses.value.find(addr => addr.type === 'pickup');
  return pickup ? pickup.value : '';
});


const transferToOrder = (carCategory, price) => {
  // Ensure allAddresses is not empty and has at least a pickup and dropoff with trimmed non-empty values.
  const validAddresses = allAddresses.value
    .map(addr => ({ ...addr, value: addr.value.trim() })) // Trim values first
    .filter(addr => addr.value !== ''); // Keep only non-empty addresses

  if (validAddresses.length < 2) {
    showToast('請至少提供兩個有效的地點（起點和終點）');
    return;
  }
  
  // Specifically check the first and last of the original list for actual input after trimming
  const firstAddress = allAddresses.value[0]?.value.trim();
  const lastAddress = allAddresses.value[allAddresses.value.length - 1]?.value.trim();

  if (!firstAddress || !lastAddress) {
    showToast('起點和終點地址不能��空');
    return;
  }

  if (price === undefined || price === null) {
    showToast('無法獲取價格，請重試估價');
    return;
  }

  // 儲存選擇的車型和價格，並打開彈窗
  selectedCarType.value = carCategory;
  selectedCarPrice.value = price;

  // 清空可能已填寫的預約資訊，或根據需求預填
  appointmentDate.value = '';
  passengerName.value = '';
  contactPhone.value = '';
  payType.value = '現金';
  detailedPickupAddressForPopup.value = pickupDisplayAddress.value; // Initialize popup address with the first address

  showReservationPopup.value = true;
};

const confirmReservationOrder = async () => {
  if (!appointmentDate.value || !passengerName.value || !contactPhone.value) {
    showToast('請填寫完整的預約資訊');
    return;
  }
  isSubmittingReservation.value = true;

  const firstAddress = allAddresses.value[0]?.value.trim();
  const lastAddress = allAddresses.value[allAddresses.value.length - 1]?.value.trim();

  const carTypeCode = selectedCarType.value === 'standard' ? '5' : (selectedCarType.value === 'van' ? '9' : '');

  const payload = {
    type: '兩地接送', 
    order_type: '3', // Specific order type for point-to-point reservation
    vendor_id: props.vendorId,
    flightno: '0000', // Default flight number
    airport: '110903', // Default airport code (e.g., Taoyuan)
    to_city_id: '110500', // Default city ID
    to_district_id: '110501', // Default district ID
    to_area_id: '110000', // Default area ID
    car_type: carTypeCode,
    
    pickupAddress: firstAddress, // 主上車地址
    dropoffAddress: lastAddress, // 主下車地址
    intermediateStops: allAddresses.value.slice(1, -1).map(addr => addr.value.trim()).filter(addr => addr !== ''),
    
    // Use the (potentially edited) address from the popup for detailed pickup
    passenger_address: detailedPickupAddressForPopup.value, 

    total_distance_km: estimateResult.value?.data?.total_distance_km,
    total_distance_text: estimateResult.value?.data?.total_distance_text,
    total_duration_text: estimateResult.value?.data?.total_duration_text,
    // legs: estimateResult.value?.data?.legs, // Usually not needed for order creation
    
    // 從彈窗獲取的資訊
    appointment_date: appointmentDate.value,
    passenger_name: passengerName.value,
    passenger_mobile: contactPhone.value,
    num_of_people: passengerCount.value.toString(),
    num_of_bags: luggageCount.value.toString(),
    pay_type: payType.value,
    
    total_price: selectedCarPrice.value,
    line_id: 'U91beb5b6a562f58c57ee1b52b1e7ada0', // Placeholder, replace with actual
    lang: 'zh-TW',
    // notes: '', // Add if notes are collected in the popup
  };

  console.log('提交兩地接送訂單數據:', payload);

  try {
    // Replace with your actual API endpoint if different
    const response = await fetch(`${API_BASE_URL}/api/orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    const result = await response.json();

    if (response.ok && result.success) {
      showToast({ type: 'success', message: result.message || '預約成功！', duration: 2000 });
      resetReservationForm();
      showReservationPopup.value = false;
    } else {
      throw new Error(result.message || '預約失敗，請稍後再試');
    }
  } catch (error) {
    showToast({ type: 'fail', message: error.message, duration: 3000 });
  } finally {
    isSubmittingReservation.value = false;
  }
};
</script>

<style scoped>
.reservation {
  padding: 12px;
}

.form-section {
  margin-bottom: 12px;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.custom-field {
  padding: 16px;
  border-bottom: 1px solid #f2f2f2;
}

.custom-field:last-child {
  border-bottom: none;
}

.address-label {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #c629e6;
  font-weight: 500;
}

.address-icon {
  margin-right: 8px;
  color: #666;
}

:deep(.van-field__control) {
  height: 24px;
  font-size: 15px;
}

.submit-area {
  margin-top: 16px;
  padding: 0 8px 16px;
}

.result-section .section-title {
  color: #4caf50; /* Green title for results */
  margin-top: 16px;
}

.api-response-pre {
  white-space: pre-wrap;       /* CSS3 */
  white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
  white-space: -pre-wrap;      /* Opera 4-6 */
  white-space: -o-pre-wrap;    /* Opera 7 */
  word-wrap: break-word;       /* Internet Explorer 5.5+ */
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.submit-button {
  background-color: #1989fa;
  border: none;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
  transition: all 0.3s ease;
  border-radius: 24px;
}

.submit-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.3);
}

:deep(.van-icon-cross) {
  color: #999;
  font-size: 16px;
  padding: 4px;
}

.custom-cell :deep(.van-cell__title) {
  color: #c629e6 !important;
  font-size: 18px;
  font-weight: 500;
  flex: 0 0 100px;
}

.add-location-icon {
  color: #1989fa;
  font-size: 18px;
  margin-right: 8px;
  cursor: pointer;
  transition: color 0.2s;
}
.add-location-icon:hover {
  color: #c629e6;
}

.add-stop-btn-row {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4px;
}
.add-stop-btn {
  background-color: #07c160;
  color: #fff;
  border: none;
  font-size: 14px;
  border-radius: 16px;
  padding: 0 12px;
  height: 28px;
  min-width: 100px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.add-stop-btn:active {
  background-color: #16ba4a;
}

.stop-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}
.move-icon {
  color: #1989fa;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.2s;
}
.move-icon:hover {
  color: #c629e6;
}
.stop-field {
  position: relative;
}

.draggable-list {
  width: 100%;
}
.drag-field {
  position: relative;
}
.drag-handle {
  cursor: grab;
  color: #c629e6;
  font-size: 20px;
  margin-right: 8px;
  transition: color 0.2s;
}
.drag-handle:active {
  color: #1989fa;
}

/* Styles for custom grid item content */
.price-grid {
  margin-bottom: 8px;
  border-radius: 8px; /* Add rounded corners to the grid container */
  overflow: hidden; /* Ensure border-radius clips van-grid-item borders */
}

.price-grid .price-grid-item :deep(.van-grid-item__content) {
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute space to push button down */
  align-items: center;
  padding: 8px 4px !important; /* Reduced padding slightly from previous */
  height: auto; /* Let content determine height */
  min-height: 100px; /* Ensure enough space for button, adjust as needed */
  box-sizing: border-box;
}

.grid-item-icon { /* Class for the van-icon inside grid-item-content */
  font-size: 24px !important; /* Icon size */
  margin-bottom: 4px !important;
}

.grid-item-content { 
  text-align: center;
  line-height: 1.2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between; /* To help push button down */
  flex-grow: 1; /* Allow content to take available space */
  width: 100%;
}

.grid-item-content .car-type-label {
  font-size: 14px; 
  font-weight: 500;
  margin-bottom: 2px;
}

.grid-item-content .price-label {
  font-size: 14px;
  color: #1989fa;
  margin-bottom: 8px; /* Space above the button */
}

.transfer-button {
  width: calc(100% - 16px); /* Button width relative to padding */
  max-width: 120px; /* Max button width */
  height: 28px;
  font-size: 13px;
  border-radius: 14px;
  margin-top: auto; /* Push button to bottom of flex container */
}


.grid-item-content .info-label {
  font-size: 11px; 
  line-height: 1.3;
}

/* Styling for route summary and legs (部分保留，部分調整) */
.route-summary {
  margin-top: 8px; /* Further Reduced margin */
}
.summary-details-group .summary-cell {
  padding-top: 8px; /* Reduced padding */
  padding-bottom: 8px; /* Reduced padding */
  background-color: #ffffff;
}
.summary-details-group .summary-cell :deep(.van-cell__title) {
  font-weight: 500;
  color: #323233;
  font-size: 14px; /* Reduced font size */
}
.summary-details-group .summary-cell :deep(.van-cell__value) {
  color: #1989fa;
  font-size: 14px; /* Reduced font size */
}

.route-legs {
  margin-top: 10px; /* Further Reduced margin */
}
.legs-title {
 color: #323233 !important; 
 margin-bottom: 8px;
 font-size: 16px; /* Reduced font size */
 font-weight: 500;
 padding-left: 4px;
}
.leg-details-group {
  margin-bottom: 10px !important; 
  border: 1px solid #e0e0e0; /* Lighter border */
  border-radius: 6px; /* Slightly smaller radius */
  overflow: hidden;
}
.leg-header-cell {
  background-color: #f9f9f9; /* Lighter header */
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
.leg-header-cell :deep(.van-cell__title) {
  font-weight: bold;
  color: #1989fa;
  font-size: 14px; /* Reduced font size */
}

/* --- 針對路段詳情優化 --- */
.leg-detail-cell { 
  padding-top: 6px !important; /* Reduced padding */
  padding-bottom: 6px !important; /* Reduced padding */
  background-color: #ffffff;
  font-size: 13px; /* Reduced base font size */
}

.address-cell :deep(.van-cell__title) {
  /* display: none; */ 
}
.address-cell :deep(.van-cell__label) { 
  width: 100%;
  margin: 0;
  padding: 0;
}
.address-leg-title {
  font-weight: 500;
  color: #555; /* Darker grey */
  margin-right: 6px; 
  display: inline-block; 
  width: 45px; /* Adjusted width */
  font-size: 13px; /* Reduced font size */
}
.address-leg-value {
  color: #333;
  white-space: normal;
  word-break: break-word;
  line-height: 1.3;
  font-size: 13px; /* Reduced font size */
}

.combined-info-cell {
  /* Making title and value parts more distinct and allowing flex control */
}
.combined-info-cell :deep(.van-cell__title) {
  display: flex;
  align-items: center;
  color: #555;
  font-size: 13px;
  font-weight: normal; 
  flex-basis: auto; /* Allow title to take its content width */
  padding-right: 5px;
}
.combined-info-cell :deep(.van-cell__value) {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align value content to the right */
  flex-grow: 1;
}

.info-leg-title {
  /* font-weight: 500; */ /* Optional: make title parts bolder */
}
.info-leg-value {
  color: #07c160; 
  /* margin-left: 4px; */ /* Add space if not using separator span */
}
.info-title-separator, .info-value-separator {
  color: #aaa; /* Lighter color for separator */
  margin: 0 3px;
}


.leg-detail-cell.combined-address-cell,
.leg-detail-cell.combined-info-cell {
  /* General styling for these cells */
}

.leg-detail-cell.combined-address-cell :deep(.van-cell__title),
.leg-detail-cell.combined-address-cell :deep(.van-cell__value) {
    /* No specific overrides needed if using slots as intended */
}

.leg-detail-cell :deep(.van-cell__title),
.leg-detail-cell :deep(.van-cell__label),
.leg-detail-cell :deep(.van-cell__value) {
  line-height: 1.5; /* Adjusted line height */
}
</style>