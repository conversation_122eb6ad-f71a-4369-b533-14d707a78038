# 表單驗證改進說明

## 問題描述
原本的表單在提交時，如果有必填欄位未填寫，會出現錯誤但沒有明確的提示給用戶，用戶體驗不佳。

## 解決方案

### 1. 改進的驗證邏輯
- **更嚴格的驗證**：使用 `trim()` 方法去除空白字符，避免只輸入空格的情況
- **更好的 Toast 提示**：使用 `Toast.fail()` 替代 `Toast()`，提供更明確的錯誤提示
- **電話號碼格式驗證**：添加基本的電話號碼格式檢查
- **航班編號邏輯優化**：當勾選「不知道」時，不再要求輸入航班編號

### 2. 視覺錯誤提示
- **欄位錯誤狀態**：為每個表單欄位添加 `:error` 屬性，當驗證失敗時會顯示紅色邊框
- **錯誤訊息顯示**：使用 `error-message` 屬性顯示具體的錯誤訊息
- **即時錯誤清除**：當用戶開始輸入或選擇時，自動清除對應欄位的錯誤狀態

### 3. 改進的用戶體驗
- **批量驗證**：一次性檢查所有欄位，避免用戶需要多次提交才能發現所有錯誤
- **焦點管理**：當欄位獲得焦點時自動清除錯誤狀態
- **更好的提示文字**：提供更具體和友好的錯誤提示

## 主要修改的文件

### `src/components/shared/ReservationInfoPopup.vue`

#### 新增的功能：
1. **錯誤狀態管理**
```javascript
const fieldErrors = ref({
  appointmentDate: false,
  flightNumber: false,
  passengerName: false,
  contactPhone: false,
  pickupAddress: false,
  payType: false
});
```

2. **改進的驗證函數**
```javascript
const handleConfirm = () => {
  // 清除之前的錯誤狀態
  clearAllErrors();
  
  let hasError = false;
  
  // 逐一驗證每個欄位
  if (!props.appointmentDateModel || props.appointmentDateModel.trim() === '') {
    fieldErrors.value.appointmentDate = true;
    Toast.fail('請選擇預約日期時間！');
    hasError = true;
  }
  
  // ... 其他欄位驗證
  
  // 如果有錯誤，不繼續執行
  if (hasError) {
    return;
  }
  
  // 所有驗證通過，發送確認事件
  emit('confirm');
};
```

3. **模板改進**
```vue
<van-field
  :model-value="props.passengerNameModel"
  @update:model-value="$emit('update:passengerNameModel', $event)"
  @focus="clearFieldError('passengerName')"
  label="訂車人大名"
  placeholder="請輸入訂車人姓名"
  required
  :error="fieldErrors.passengerName"
  error-message="請輸入訂車人大名"
/>
```

## 驗證規則

### 必填欄位驗證：
1. **預約日期時間** - 必須選擇
2. **班機編號** - 當顯示且未勾選「不知道」時必填
3. **訂車人大名** - 必填，不能為空或只有空格
4. **連絡電話** - 必填，且需符合基本格式 (8-15位數字、+、-、空格、括號)
5. **上車/下車地址** - 必填，不能為空或只有空格
6. **付款方式** - 必須選擇「現金」或「信用卡」

### 特殊邏輯：
- 航班編號：當勾選「不知道」時，自動設為 '--' 且不進行驗證
- 電話號碼：基本格式驗證，允許國際格式
- 地址標籤：根據 inquiryType 動態顯示「上車地址」或「下車地址」

## 測試方法

1. 啟動開發服務器：`npm run dev`
2. 打開瀏覽器訪問應用
3. 進入任何包含 ReservationInfoPopup 的頁面
4. 點擊預約按鈕打開彈窗
5. 直接點擊「提交訂單」按鈕
6. 觀察是否出現：
   - Toast 錯誤提示
   - 欄位紅色邊框
   - 具體的錯誤訊息

## 後續建議

1. **添加更多驗證規則**：
   - 電話號碼的更嚴格驗證
   - 姓名格式驗證
   - 地址長度限制

2. **改進用戶體驗**：
   - 添加載入狀態
   - 優化錯誤提示的顯示時間
   - 添加成功提示

3. **無障礙改進**：
   - 添加 ARIA 標籤
   - 改進鍵盤導航
   - 螢幕閱讀器支援

## 相容性說明

這些改進完全向後相容，不會影響現有的功能。所有父組件都可以正常使用，無需修改。
