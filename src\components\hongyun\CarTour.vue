<template>
  <div class="car-tour">
    <div class="form-section">
      <!-- <div class="section-title">基本信息</div> -->
      <van-cell-group inset>

        <van-cell title="包車" class="custom-cell">
          <template #value>
            <van-radio-group v-model="tourType" direction="horizontal" class="custom-radio-group">
              <van-radio name="一日" class="custom-radio">一日</van-radio>
              <van-radio name="半日" class="custom-radio">半日</van-radio>
            </van-radio-group>
          </template>
        </van-cell>
        <van-cell title="車型" class="custom-cell">
          <template #value>
            <van-radio-group v-model="carType" direction="horizontal" class="custom-radio-group">
              <van-radio name="五人座" class="custom-radio">五人座</van-radio>
              <van-radio name="五人休旅" class="custom-radio">五人休旅</van-radio>
              <van-radio name="九人座" class="custom-radio">九人座</van-radio>
            </van-radio-group>
          </template>
        </van-cell>

        <van-field
          v-model="area"
          required
          name="area"
          label="上車地址"
          placeholder="請輸入上車地址"
          class="custom-field"
        />
      </van-cell-group>
    </div>

    <div class="form-section">
      <!-- <div class="section-title">人數與行李</div> -->
      <van-cell-group inset>
        <div class="compact-container">
          <div class="compact-row no-border">
            <div class="inline-cell">
              <span class="inline-label">乘客人數</span>
              <van-stepper v-model="passengerCount" :min="1" theme="round" button-size="20" class="compact-stepper" />
            </div>
            <div class="inline-cell">
              <span class="inline-label">行李數</span>
              <van-stepper v-model="luggageCount" :min="0" theme="round" button-size="20" class="compact-stepper" />
            </div>
          </div>

          <div class="compact-row no-padding-top">
            <div class="inline-cell">
              <span class="inline-label">兒童座椅</span>
              <van-stepper
                v-model="childSeatCount"
                :min="0"
                :max="maxChildSeatCount"
                theme="round"
                button-size="20"
                class="compact-stepper"
              />
            </div>
            <div class="inline-cell">
              <span class="inline-label">增高墊</span>
              <van-stepper
                v-model="boosterSeatCount"
                :min="0"
                :max="maxBoosterSeatCount"
                theme="round"
                button-size="20"
                class="compact-stepper"
              />
            </div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <div class="form-section">
      <van-cell-group inset>
        <van-field
          v-model="notes"
          name="notesCarTour"
          label="備註說明"
          rows="2"
          autosize
          type="textarea"
          maxlength="100"
          placeholder="請輸入註記事項"
          show-word-limit
          class="custom-field custom-textarea"
        />
      </van-cell-group>
      <van-submit-bar
        :price="totalPrice * 100"
        button-text="我要預約"
        @submit="proceedToOrder"
        currency="NT$"
        :disabled="!area.trim()"
        decimal-length="1"
      />
    </div>

    <!-- 使用可重複使用的預約資訊彈窗 -->
    <ReservationInfoPopup
      v-model:show="showCarTourReservationPopup"
      :total-price="totalPrice"
      @confirm="confirmCarTourOrder"
      :confirm-loading="isSubmittingCarTour"
      v-model:appointmentDateModel="departureDate" 
      v-model:passengerNameModel="passengerName"
      v-model:contactPhoneModel="contactPhone"
      v-model:pickupAddressModel="area" 
      v-model:payTypeModel="payType"
      :inquiryType="'包車旅遊'" 
      title="包車預約資訊"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { showDialog, showToast } from 'vant'; // Added showToast

const ADDITIONAL_CHARGES = {
  childSeat: 100, // 兒童座椅
  boosterSeat: 100 // 增高墊
};
const API_BASE_URL = import.meta.env.VITE_API_SERVER;
import ReservationInfoPopup from '../shared/ReservationInfoPopup.vue'; // 引入新元件
// import { useRouter } from 'vue-router'; // For navigation
// const router = useRouter(); // For navigation

// 接收 props
const props = defineProps({
  vendorId: {
    type: [String, Number],
    default: '2'
  }
});

// 新增彈窗相關狀態
const showCarTourReservationPopup = ref(false);
const isSubmittingCarTour = ref(false);
// 包車旅遊預約時，可能也需要訂車人姓名和電話
const passengerName = ref('');
const contactPhone = ref('');
const flightNumber = ref('0000'); // 新增 flightNumber 並設定預設值
const payType = ref('現金'); // 預設現金支付

// 定義響應式狀態
const carType = ref('五人座');
const passengerCount = ref(1);
const luggageCount = ref(0);
const childSeatCount = ref(0);
const boosterSeatCount = ref(0);

const maxChildSeatCount = computed(() => {
  return 2 - boosterSeatCount.value;
});

const maxBoosterSeatCount = computed(() => {
  return 2 - childSeatCount.value;
});

const tourDays = ref(1);
const tourType = ref('一日');
// const tourDescription = ref('');
// tourDescription = ref('');

const area = ref('');
const notes = ref('');

const hourlyRate = computed(() => {
  switch (carType.value) {
    case '五人座':
      return 500;
    case '五人休旅':
      return 600;
    case '九人座':
      return 700;
    default:
      return 0;
  }
});

const tourHours = computed(() => {
  switch (tourType.value) {
    case '一日':
      return 10;
    case '半日':
      return 5;
    default:
      return 0;
  }
});

const baseFare = computed(() => {
  return hourlyRate.value * tourHours.value;
});

const additionalFees = computed(() => {
  return (childSeatCount.value * ADDITIONAL_CHARGES.childSeat) + (boosterSeatCount.value * ADDITIONAL_CHARGES.boosterSeat);
});

const totalPrice = computed(() => {
  return baseFare.value + additionalFees.value;
});
// 組件掛載時輸出 vendor_id 用於調試
onMounted(() => {
  console.log('CarTour 組件掛載，vendor_id:', props.vendorId);
  // fetchAreaData();
});

// 日期選擇相關
const showDatePicker = ref(false);
const selectedDate = ref(new Date());
const departureDate = ref('');
const minDate = new Date();
const maxDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1));

// 日期確認處理
const onDateConfirm = (value) => {
  departureDate.value = `${value.getFullYear()}-${String(value.getMonth() + 1).padStart(2, '0')}-${String(value.getDate()).padStart(2, '0')}`;
  showDatePicker.value = false;
};

const proceedToOrder = () => { // 這個方法現在只負責打開彈窗
  if (!area.value.trim()) {
    showToast('請輸入上車地址');
    return;
  }
  // 可以在這裡預先填充一些彈窗內可能需要的欄位，如果它們有預設值或可以從當前表單獲取
  // 例如，如果 departureDate 是必填的，可以在這裡檢查
  showCarTourReservationPopup.value = true;
};

const resetCarTourForm = () => {
  tourType.value = '一日';
  carType.value = '五人座';
  area.value = '';
  passengerCount.value = 1;
  luggageCount.value = 0;
  childSeatCount.value = 0;
  boosterSeatCount.value = 0;
  departureDate.value = '';
  passengerName.value = '';
  contactPhone.value = '';
  flightNumber.value = '0000'; // 重置時也設為預設值
  payType.value = '現金';
  notes.value = ''; // Reset notes
  // totalPrice, baseFare, additionalFees 是計算屬性，會自動更新
};

const confirmCarTourOrder = async () => { // 實際提交邏輯
  if (!departureDate.value || !passengerName.value || !contactPhone.value || !area.value.trim()) {
    showToast('請填寫完整的預約資訊');
    return; 
  }
  isSubmittingCarTour.value = true;

  let resolvedCarTypeCode = '';
    switch (carType.value) {
      case '五人座': 
        resolvedCarTypeCode = '5';
        break;
      case '五人休旅':
        resolvedCarTypeCode = '51';
        break;
      case '九人座':
        resolvedCarTypeCode = '9';
        break;
      default:
        resolvedCarTypeCode = '';
    }

  let resolvedTourTypeCode = '';
    switch (tourType.value) {
      case '一日': resolvedTourTypeCode = 1; break; // Full Day
      case '半日': resolvedTourTypeCode = 2; break; // Half Day
      default: resolvedTourTypeCode = '';
    }

  const payload = {
    type: '包車旅遊', // 服務總類型
    order_type: '2', // 包車旅遊的特定訂單類型
    airport: '110903',
    to_city_id: '110500',
    to_district_id: '110501',
    to_area_id: '110000',
    vendor_id: props.vendorId,
    tour_type: resolvedTourTypeCode, 
    car_type: resolvedCarTypeCode,   
    pickup_address: area.value,    // 上車地點 (包車通常只有一個主要地點)
    num_of_people: passengerCount.value.toString(),
    num_of_bags: luggageCount.value.toString(),
    child_seat: childSeatCount.value.toString(),
    booster_pad: boosterSeatCount.value.toString(),
    flightno: flightNumber.value, // 新增 flightNumber 到 payload
    appointment_date: departureDate.value, // 預約日期時間
    passenger_name: passengerName.value,
    passenger_mobile: contactPhone.value,
    passenger_address: area.value, // 使用表單中的上車地址作為乘客地址
    total_price: totalPrice.value,
    notes: notes.value, // 備註送到後端
    // base_fare: baseFare.value, // 這些可能是前端計算的，後端可能不需要或會自己算
    // additional_fees: additionalFees.value,
    line_id: 'U91beb5b6a562f58c57ee1b52b1e7ada0', // 根據實際情況修改
    pay_type: payType.value,
    lang: 'zh-TW',
    // notes: '', // 如果 ReservationInfoPopup 將來支持備註，可以在此添加
  };

  console.log('提交包車訂單數據:', payload);

  try {
    // 假設 API 端點與機場接送相同，如果不同請修改
    const response = await fetch(`${API_BASE_URL}/api/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showToast({
        type: 'success',
        message: result.message || '包車預約成功！',
        duration: 2000
      });
      resetCarTourForm(); // 新增一個重置表單的函數
      showCarTourReservationPopup.value = false;
    } else {
      throw new Error(result.message || '包車預約失敗，請稍後再試');
    }
  } catch (error) {
    showToast({ type: 'fail', message: error.message, duration: 3000 });
  } finally {
    isSubmittingCarTour.value = false;
  }
};

</script>

<style scoped>
.car-tour {
  padding: 12px;
}

.form-section {
  margin-bottom: 12px;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.custom-cell {
  padding: 16px;
  border-bottom: 1px solid #f2f2f2;
}

.custom-cell:last-child {
  border-bottom: none;
}

.custom-cell :deep(.van-cell__title) {
  color: #333 !important;
  font-size: 16px;
  font-weight: 500;
  flex: 0 0 100px;
}

.required-field::before {
  content: '*';
  color: #ee0a24;
  margin-right: 4px;
}

.location-cell :deep(.van-cell__value) {
  color: #1989fa;
}

.custom-icon {
  color: #1989fa;
  font-size: 16px;
}

.custom-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.custom-radio {
  margin-right: 16px;
  margin-bottom: 8px;
}

.custom-radio :deep(.van-radio__label) {
  color: #333;
}

.custom-radio :deep(.van-radio__icon--checked .van-icon) {
  background-color: #1989fa;
  border-color: #1989fa;
}

.compact-container {
  padding: 4px 0;
}

.compact-row {
  display: flex;
  padding: 4px 16px;
  border-bottom: 1px solid #f2f2f2;
}

.compact-row.no-border {
  border-bottom: none;
  padding-bottom: 2px;
}

.compact-row.no-padding-top {
  padding-top: 2px;
}

.compact-row:last-child {
  border-bottom: none;
}

.inline-cell {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
}

.inline-cell:first-child {
  padding-left: 0;
}

.inline-cell:last-child {
  padding-right: 0;
}

.inline-label {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-bottom: 0;
}

.compact-stepper {
  width: 75px;
  margin-left: 6px;
}

.compact-stepper :deep(.van-stepper__minus),
.compact-stepper :deep(.van-stepper__plus) {
  background-color: #1989fa;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 10px;
}

.compact-stepper :deep(.van-stepper__input) {
  background-color: #f5f7fa;
  height: 20px;
  width: 24px;
  font-size: 13px;
  margin: 0 4px;
}

/* .custom-textarea {
  border-radius: 8px;
  width: 100%;
} */

.custom-textarea :deep(.van-field__control) {
  min-height: 60px;
  font-size: 14px;
  padding: 8px;
  background-color: #f5f7fa;
}

.submit-area {
  margin-top: 16px;
  padding: 0 8px 16px;
}

.submit-button {
  background-color: #1989fa;
  border: none;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
  transition: all 0.3s ease;
  border-radius: 24px;
}

.submit-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.3);
}

.custom-cell :deep(.van-cell__title) {
  color: #c629e6 !important;
  font-size: 18px;
  font-weight: 500;
  flex: 0 0 100px;
}

.submit-area-cartour {
  /* Add any specific styling for the submit bar container if needed */
  /* For example, to ensure it doesn't overlap with content if page is short */
  padding-bottom: 50px; /* Adjust if submit bar height is different */
}

.submit-area-cartour .van-cell-group--inset {
  margin-bottom: 0;
  border-radius: 8px;
  box-shadow: none;
}
.submit-area-cartour .custom-field {
  margin-bottom: 0;
}

/* Ensure van-submit-bar itself is fixed if not by default or overridden */
:deep(.van-submit-bar) {
  position: fixed; /* Ensure it stays at the bottom */
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100; /* Ensure it's above other content */
}

.notes-section {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px 12px 4px 12px;
  margin-bottom: 8px;
}
.notes-title {
  font-size: 15px;
  font-weight: 500;
  color: #c629e6;
  margin-bottom: 4px;
}
</style>
