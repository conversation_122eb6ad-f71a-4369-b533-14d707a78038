<template>
  <div>
    <Navbar
      :title="navbarTitle"
      :left-text="navbarLeftText"
      :right-text="navbarRightText"
      :background-color="navbarBackgroundColor"
      @click-left="onClickLeft"
      @click-right="onClickRight"
    />
    <router-view />

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Tabbar, TabbarItem } from 'vant';
import Navbar from './components/Navbar.vue';
// import liff from '@line/liff';

const route = useRoute();
const router = useRouter();

// 底部標籤欄激活狀態
const activeTab = ref('CarOrder');

// const liffId = "2002237021-Q4on41oL";

onMounted(async () => {
  // await initializeLiff(); // 解除註釋 LIFF 初始化
});

const initializeLiff = async () => {
  try {
    await liff.init({
      liffId: liffId
    });
    // Check if the user is logged in
    if (!liff.isLoggedIn()) {
      liff.login();
    }

    const storedTab = await liff.getLocalStorage('activeTab');
    if (storedTab) {
      activeTab.value = storedTab;
    }
  } catch (err) {
    console.error("LIFF Initialization failed", err);
    showToast({
      message: `LIFF初始化失敗: ${err.message}`,
      position: 'top'
    });
  }
};

// 監聽路由變化，更新底部標籤欄激活狀態
watch(() => route.name, async (newRouteName) => {
  if (newRouteName) {
    activeTab.value = newRouteName;
    // Store the active tab in Liff storage
    // await liff.setLocalStorage('activeTab', newRouteName);
  }
}, { immediate: true });

// 計算導航欄標題
const navbarTitle = computed(() => {
  switch (route.name) {
    case 'CarQuotaHome': // 新增 CarQuotaHome 情況
      return '車輛租賃服務';
    case 'CarOrder':
      return '預約車輛';
    case 'MyCarOrder':
      return 'My Car Order';
    case 'CarQuota':
      return '機場接送服務';
    default:
      return '';
  }
});

// 計算導航欄左側文字
const navbarLeftText = computed(() => {
  switch (route.name) {
    case 'CarOrder':
      return 'Back';
    default:
      return '';
  }
});

// 其他導航欄屬性
const navbarRightText = ref('');

// 根據路由設置不同的導航欄背景顏色
const navbarBackgroundColor = computed(() => {
  switch (route.name) {
    case 'CarQuota':
      return '#1989fa'; // 藍色主題
    default:
      return '#4CAF50'; // 綠色主題
  }
});

// 事件處理函數
const onClickLeft = () => {
  router.go(-1);
};

const onClickRight = () => {
  // TODO: Add right button click logic
};
</script>

<style>
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f7fa;
}

:deep(.van-tabbar) {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

:deep(.van-tabbar-item--active) {
  color: var(--van-tabbar-item-active-color, #1989fa);
}

:deep(.van-tabbar-item) {
  font-size: 12px;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
