# 紅色錯誤文字修復測試指南

## 🎯 修復目標

**用戶需求**: 紅色錯誤文字不要一直出現，只有在真正有錯誤時才顯示

**問題現象**: 即使用戶已經填寫了內容，紅色錯誤文字仍然持續顯示

## 🔧 修復內容

### 1. 修正航班編號驗證邏輯
**問題**: 驗證邏輯錯誤地把 `'--'` 當作無效值
```javascript
// 修改前
if (!props.flightNumberModel || props.flightNumberModel.trim() === '' || props.flightNumberModel.trim() === '--') {

// 修改後  
if (!props.flightNumberModel || props.flightNumberModel.trim() === '') {
```

### 2. 添加主動錯誤清除機制
**新增功能**: 監聽所有欄位值的變化，當內容有效時自動清除錯誤狀態

```javascript
// 監聽各個欄位的值變化，自動清除錯誤狀態
watch(() => props.appointmentDateModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('appointmentDate');
  }
});

watch(() => props.passengerNameModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('passengerName');
  }
});

// ... 其他欄位類似
```

## 🧪 測試步驟

### 測試 1: 基本錯誤狀態清除

#### 步驟：
1. 打開 http://127.0.0.1:5174/
2. 選擇「機場接送」標籤
3. 選擇接送類型和區域
4. 點擊「查詢價格」
5. 點擊「預約」按鈕
6. **直接點擊「提交訂單」**（不填寫任何資料）
7. 觀察所有欄位顯示紅色錯誤文字
8. **逐一填寫每個欄位**：
   - 選擇日期時間
   - 勾選航班「不知道」或輸入航班編號
   - 輸入訂車人大名
   - 輸入連絡電話
   - 輸入地址
   - 選擇付款方式

#### 預期結果：
- ✅ 每填寫一個欄位，該欄位的紅色錯誤文字立即消失
- ✅ 只有未填寫的欄位才顯示紅色錯誤文字
- ✅ 所有欄位填寫完成後，沒有任何紅色錯誤文字

### 測試 2: 航班「不知道」功能

#### 步驟：
1. 觸發表單驗證錯誤（直接提交空表單）
2. 觀察航班編號欄位顯示紅色錯誤文字
3. **勾選「不知道」選項**
4. 觀察航班編號欄位的錯誤狀態

#### 預期結果：
- ✅ 勾選「不知道」後，航班編號的紅色錯誤文字立即消失
- ✅ 航班編號欄位變為禁用狀態
- ✅ 航班編號值顯示為 `--`

### 測試 3: 日期時間選擇

#### 步驟：
1. 觸發表單驗證錯誤
2. 觀察日期時間欄位顯示紅色錯誤文字
3. **點擊日期時間欄位**
4. **選擇日期和時間**
5. **點擊確認**

#### 預期結果：
- ✅ 選擇日期時間後，該欄位的紅色錯誤文字立即消失
- ✅ 欄位顯示選擇的日期時間

### 測試 4: 付款方式選擇

#### 步驟：
1. 觸發表單驗證錯誤
2. 觀察付款方式欄位顯示紅色錯誤文字
3. **選擇「現金」或「信用卡」**

#### 預期結果：
- ✅ 選擇付款方式後，該欄位的紅色錯誤文字立即消失

### 測試 5: 完整流程測試

#### 步驟：
1. 直接提交空表單，觸發所有錯誤
2. 按順序填寫所有欄位
3. 觀察每個欄位的錯誤狀態變化
4. 最終提交完整表單

#### 預期結果：
- ✅ 每個欄位填寫後錯誤狀態立即清除
- ✅ 最終沒有任何紅色錯誤文字
- ✅ 表單可以成功提交

## 🔍 除錯方法

### 如果紅色錯誤文字仍然顯示：

1. **檢查 fieldErrors 狀態**：
   ```javascript
   // 在瀏覽器控制台執行
   console.log('fieldErrors:', fieldErrors.value);
   ```

2. **檢查欄位值**：
   ```javascript
   console.log('appointmentDate:', props.appointmentDateModel);
   console.log('flightNumber:', props.flightNumberModel);
   console.log('passengerName:', props.passengerNameModel);
   console.log('contactPhone:', props.contactPhoneModel);
   console.log('pickupAddress:', props.pickupAddressModel);
   console.log('payType:', props.payTypeModel);
   ```

3. **檢查 watch 函數是否觸發**：
   - 在 watch 函數中添加 console.log
   - 確認值變化時 watch 被調用

### 如果特定欄位錯誤狀態不清除：

1. **檢查該欄位的 watch 邏輯**
2. **檢查 clearFieldError 函數是否被調用**
3. **檢查欄位值是否符合清除條件**

## ✅ 驗證清單

### 錯誤狀態清除
- [ ] 日期時間選擇後錯誤清除
- [ ] 航班編號輸入後錯誤清除
- [ ] 航班「不知道」勾選後錯誤清除
- [ ] 訂車人大名輸入後錯誤清除
- [ ] 連絡電話輸入後錯誤清除
- [ ] 地址輸入後錯誤清除
- [ ] 付款方式選擇後錯誤清除

### 用戶體驗
- [ ] 錯誤狀態清除及時（立即反應）
- [ ] 只有真正有錯誤的欄位才顯示紅色文字
- [ ] 填寫完整後沒有任何紅色錯誤文字
- [ ] 表單可以正常提交

### 回歸測試
- [ ] 原有驗證功能正常
- [ ] 空欄位仍會顯示錯誤
- [ ] 無效內容仍會顯示錯誤

## 🎯 成功標準

當以下條件都滿足時，修復成功：

1. ✅ 紅色錯誤文字只在真正有錯誤時顯示
2. ✅ 填寫內容後錯誤文字立即消失
3. ✅ 所有欄位都有此行為
4. ✅ 表單驗證功能正常工作

---

**測試重點**: 確保紅色錯誤文字的顯示邏輯正確  
**預期結果**: 用戶填寫內容後，錯誤提示立即消失
