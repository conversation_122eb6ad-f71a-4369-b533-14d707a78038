<template>
  <van-popup
    :show="show"
    position="bottom"
    closeable
    :style="{ height: 'auto', maxHeight: '70%', 'background-color': 'khaki' }"
    round
    @update:show="onUpdateShow"
    @closed="onClosed"
  >
    <div class="reservation-info-popup-container">
      <div class="popup-header-section">
        <h3 class="popup-title">{{ title }}</h3>
        <div v-if="totalPrice !== null && totalPrice !== undefined" class="popup-total-price">
          合計: <span class="price-amount">NT$ {{ totalPrice }}</span>
        </div>
      </div>

      <div class="popup-content-section">
        <van-cell-group inset class="details-cell-group">
          <van-field
            :model-value="props.appointmentDateModel"
            readonly
            is-link
            label="日期時間"
            placeholder="請選擇預約日期時間"
            required
            @click="openDateTimePicker"
            @focus="clearFieldError('appointmentDate')"
          />
          <van-field
            v-if="shouldShowFlightNumber"
            :model-value="props.flightNumberModel"
            required
            :disabled="flightNumberUnknown"
            @update:model-value="(value) => { clearFieldError('flightNumber'); $emit('update:flightNumberModel', value); }"
            @focus="clearFieldError('flightNumber')"
            label="班機編號"
            placeholder="請輸入班機編號或勾選「不知道」"
          >
            <template #button>
              <van-checkbox v-model="flightNumberUnknown" style="margin-left:8px;">不知道</van-checkbox>
            </template>
          </van-field>
          <van-field
            :model-value="props.passengerNameModel"
            @update:model-value="(value) => { clearFieldError('passengerName'); $emit('update:passengerNameModel', value); }"
            @focus="clearFieldError('passengerName')"
            label="訂車人大名"
            placeholder="請輸入訂車人姓名"
            required
          />
          <van-field
            :model-value="props.contactPhoneModel"
            @update:model-value="(value) => { clearFieldError('contactPhone'); $emit('update:contactPhoneModel', value); }"
            @focus="clearFieldError('contactPhone')"
            label="連絡電話"
            type="tel"
            placeholder="請輸入連絡電話"
            required
          />
          <template v-if="inquiryType === '兩地接送'">
            <van-field label="乘客人數">
              <template #input>
                <van-stepper :model-value="props.passengerCountModel" @update:model-value="$emit('update:passengerCountModel', $event)" :min="1" />
              </template>
            </van-field>
            <van-field label="行李數">
              <template #input>
                <van-stepper :model-value="props.luggageCountModel" @update:model-value="$emit('update:luggageCountModel', $event)" :min="0" />
              </template>
            </van-field>
          </template>
          <van-field
            :model-value="props.pickupAddressModel"
            @update:model-value="(value) => { clearFieldError('pickupAddress'); $emit('update:pickupAddressModel', value); }"
            @focus="clearFieldError('pickupAddress')"
            :title="pickupAddressLabel"
            :label="pickupAddressLabel"
            placeholder="請輸入詳細地址"
            required
            :error="fieldErrors.pickupAddress"
            error-message="請輸入詳細地址"
          />
          <van-field
            name="payTypeRadio"
            label="付款方式"
            class="custom-field-popup"
            :error="fieldErrors.payType"
            error-message="請選擇付款方式"
          >
            <template #input>
              <van-radio-group
                :model-value="props.payTypeModel"
                @update:model-value="(value) => { clearFieldError('payType'); $emit('update:payTypeModel', value); }"
                direction="horizontal"
              >
                <van-radio name="現金">現金</van-radio>
                <van-radio name="信用卡" style="margin-left: 10px;">信用卡</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
      </div>

      <div class="popup-footer-buttons">
        <van-button
          type="default"
          size="large"
          @click="handleCancel"
        >
          {{ cancelButtonText }}
        </van-button>
        <van-button
          type="primary"
          size="large"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          {{ confirmButtonText }}
        </van-button>
      </div>
    </div>

    <!-- 日期時間選擇彈窗 -->
    <van-popup v-model:show="showDateTimePickerPopup" position="bottom" round>
      <van-picker-group
        title="選擇日期和時間"
        :tabs="['選擇日期', '選擇時間']"
        @confirm="onDateTimePickerConfirm"
        @cancel="onDateTimePickerCancel"
        confirm-button-text="確認"
        cancel-button-text="取消"
        class="custom-area-picker"
      >
        <van-date-picker
          v-model="currentDateForPicker"
          :min-date="minDate"
          :max-date="maxDate"
        />
        <van-time-picker
          v-model="currentTimeForPicker"
          :filter="timeFilter"
        />
      </van-picker-group>
    </van-popup>
  </van-popup>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { showToast } from 'vant';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '預約資訊確認',
  },
  // Props for v-model binding
  appointmentDateModel: {
    type: String,
    default: ''
  },
  flightNumberModel: {
    type: String,
    default: ''
  },
  passengerNameModel: {
    type: String,
    default: ''
  },
  passengerCountModel: {
    type: Number,
    default: 1
  },
  luggageCountModel: {
    type: Number,
    default: 0
  },
  contactPhoneModel: {
    type: String,
    default: ''
  },
  pickupAddressModel: {
    type: String,
    default: ''
  },
  payTypeModel: {
    type: String,
    default: '0' // Default to '現金'
  },
  // Non-editable display info
  totalPrice: {
    type: [Number, String],
    default: null,
  },
  confirmButtonText: {
    type: String,
    default: '提交訂單',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  confirmLoading: {
    type: Boolean,
    default: false,
  },
  inquiryType: { // Needed for pickupAddressLabel
    type: String,
    default: '送機'
  }
});

const emit = defineEmits([
  'update:show',
  'confirm',
  'cancel',
  'closed',
  'update:appointmentDateModel',
  'update:flightNumberModel',
  'update:passengerNameModel',
  'update:passengerCountModel',
  'update:luggageCountModel',
  'update:contactPhoneModel',
  'update:pickupAddressModel',
  'update:payTypeModel'
]);

// 錯誤狀態管理
const fieldErrors = ref({
  appointmentDate: false,
  flightNumber: false,
  passengerName: false,
  contactPhone: false,
  pickupAddress: false,
  payType: false
});

// 清除所有錯誤狀態
const clearAllErrors = () => {
  Object.keys(fieldErrors.value).forEach(key => {
    fieldErrors.value[key] = false;
  });
};

// 清除特定欄位的錯誤狀態
const clearFieldError = (fieldName) => {
  if (fieldErrors.value[fieldName]) {
    fieldErrors.value[fieldName] = false;
  }
};

// Watch for the popup to open and pre-fill data from localStorage
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 清除所有錯誤狀態
    clearAllErrors();

    const savedName = localStorage.getItem('passengerName');
    if (savedName && !props.passengerNameModel) {
      emit('update:passengerNameModel', savedName);
    }

    const savedPhone = localStorage.getItem('contactPhone');
    if (savedPhone && !props.contactPhoneModel) {
      emit('update:contactPhoneModel', savedPhone);
    }

    const savedAddress = localStorage.getItem('pickupAddress');
    if (savedAddress && !props.pickupAddressModel) {
      emit('update:pickupAddressModel', savedAddress);
    }
  }
});

const onUpdateShow = (value) => {
  emit('update:show', value);
};

const onClosed = () => {
  emit('closed');
};

const handleConfirm = () => {
  console.log('[Popup] handleConfirm triggered. Date:', props.appointmentDateModel);

  // 清除之前的錯誤狀態
  clearAllErrors();

  let hasError = false;

  // 驗證預約日期
  if (!props.appointmentDateModel || props.appointmentDateModel.trim() === '') {
    fieldErrors.value.appointmentDate = true;
    showToast({ type: 'fail', message: '請選擇預約日期時間！' });
    hasError = true;
  }

  // 根據需要驗證航班編號
  if (shouldShowFlightNumber.value) {
    // 如果沒有勾選「不知道」，則需要驗證航班編號輸入
    if (!flightNumberUnknown.value) {
      if (!props.flightNumberModel || props.flightNumberModel.trim() === '') {
        fieldErrors.value.flightNumber = true;
        showToast({ type: 'fail', message: '請輸入班機編號或勾選「不知道」！' });
        hasError = true;
      }
    }
    // 如果勾選了「不知道」，確保 flightNumberModel 是 '--'
    else if (props.flightNumberModel !== '--') {
      // 這種情況下自動設置為 '--'，不算錯誤
      emit('update:flightNumberModel', '--');
    }
  }

  // 驗證其他必填欄位
  if (!props.passengerNameModel || props.passengerNameModel.trim() === '') {
    fieldErrors.value.passengerName = true;
    showToast({ type: 'fail', message: '請輸入訂車人大名！' });
    hasError = true;
  }

  if (!props.contactPhoneModel || props.contactPhoneModel.trim() === '') {
    fieldErrors.value.contactPhone = true;
    showToast({ type: 'fail', message: '請輸入連絡電話！' });
    hasError = true;
  } else {
    // 驗證電話號碼格式（基本驗證）
    const phoneRegex = /^[0-9+\-\s()]{8,15}$/;
    if (!phoneRegex.test(props.contactPhoneModel.trim())) {
      fieldErrors.value.contactPhone = true;
      showToast({ type: 'fail', message: '請輸入有效的連絡電話！' });
      hasError = true;
    }
  }

  if (!props.pickupAddressModel || props.pickupAddressModel.trim() === '') {
    fieldErrors.value.pickupAddress = true;
    showToast({ type: 'fail', message: '請輸入上車/下車地址！' });
    hasError = true;
  }

  // 驗證付款方式
  if (!props.payTypeModel || (props.payTypeModel !== '現金' && props.payTypeModel !== '信用卡')) {
    fieldErrors.value.payType = true;
    showToast({ type: 'fail', message: '請選擇付款方式！' });
    hasError = true;
  }

  // 如果有錯誤，不繼續執行
  if (hasError) {
    return;
  }

  // Save data to localStorage before confirming
  try {
    localStorage.setItem('passengerName', props.passengerNameModel.trim());
    localStorage.setItem('contactPhone', props.contactPhoneModel.trim());
    localStorage.setItem('pickupAddress', props.pickupAddressModel.trim());
  } catch (error) {
    console.warn('無法保存到 localStorage:', error);
  }

  // 所有驗證通過，發送確認事件
  emit('confirm');
};

const handleCancel = () => {
  emit('update:show', false); // Close the popup
  emit('cancel');
};

const shouldShowFlightNumber = computed(() => {
  return props.inquiryType === '送機' || props.inquiryType === '接機' || props.inquiryType === '來回';
});

// --- Date Time Picker Logic ---
const showDateTimePickerPopup = ref(false);
const today = new Date();
const minDate = new Date(today.getFullYear(), today.getMonth(), today.getDate()); // Min date is today
const maxDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1)); // Max date is one year from now

const currentDateForPicker = ref([
  String(today.getFullYear()),
  String(today.getMonth() + 1).padStart(2, '0'),
  String(today.getDate()).padStart(2, '0')
]);
const currentTimeForPicker = ref(['12', '00']);

const timeFilter = (type, options) => {
  if (type === 'minute') {
    return options.filter(option => {
      const value = typeof option === 'object' && option.value !== undefined ? option.value : option;
      return Number(value) % 5 === 0;
    });
  }
  return options;
};

const openDateTimePicker = () => {
  // Initialize picker with current model value if available
  if (props.appointmentDateModel) {
    const [datePart, timePart] = props.appointmentDateModel.split(' ');
    if (datePart) {
      currentDateForPicker.value = datePart.split('-');
    }
    if (timePart) {
      currentTimeForPicker.value = timePart.split(':');
    }
  } else {
    // Reset to default if no model value
    currentDateForPicker.value = [String(today.getFullYear()), String(today.getMonth() + 1).padStart(2, '0'), String(today.getDate()+1).padStart(2, '0')];
    currentTimeForPicker.value = ['09', '00'];
  }
  showDateTimePickerPopup.value = true;
};

const onDateTimePickerConfirm = () => {
  const formattedDate = currentDateForPicker.value.join('-');
  const formattedTime = currentTimeForPicker.value.join(':');
  clearFieldError('appointmentDate');
  emit('update:appointmentDateModel', `${formattedDate} ${formattedTime}`);
  showDateTimePickerPopup.value = false;
};

const onDateTimePickerCancel = () => {
  showDateTimePickerPopup.value = false;
};

const pickupAddressLabel = computed(() => {
  return props.inquiryType === '接機' ? '下車地址' : '上車地址';
});

const flightNumberUnknown = ref(false);

// 監聽 flightNumberUnknown 的變化
watch(flightNumberUnknown, (val) => {
  clearFieldError('flightNumber');
  if (val) {
    emit('update:flightNumberModel', '--');
  } else if (props.flightNumberModel === '--') {
    emit('update:flightNumberModel', '');
  }
});

// 監聽 flightNumberModel 的變化，同步 flightNumberUnknown 狀態
watch(() => props.flightNumberModel, (newVal) => {
  if (newVal === '--') {
    flightNumberUnknown.value = true;
  } else if (flightNumberUnknown.value && newVal !== '--') {
    flightNumberUnknown.value = false;
  }
});

// 監聽各個欄位的值變化，自動清除錯誤狀態
watch(() => props.appointmentDateModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('appointmentDate');
  }
});

watch(() => props.passengerNameModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('passengerName');
  }
});

watch(() => props.contactPhoneModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('contactPhone');
  }
});

watch(() => props.pickupAddressModel, (newVal) => {
  if (newVal && newVal.trim() !== '') {
    clearFieldError('pickupAddress');
  }
});

watch(() => props.payTypeModel, (newVal) => {
  if (newVal && (newVal === '現金' || newVal === '信用卡')) {
    clearFieldError('payType');
  }
});

</script>

<style scoped>

.custom-area-picker :deep(.van-picker__toolbar) {
  background-color: #f2f399;
}
.custom-area-picker :deep(.van-picker__columns) {
  background-color: #ececbe;
}

.custom-area-picker :deep(.van-picker__title) {
  color: #4CAF50;
  font-weight: 600;
}

.custom-area-picker :deep(.van-picker__confirm) {
  color: #4CAF50;
}

.custom-area-picker :deep(.van-picker-column__item--selected) {
  color: #4CAF50;
  font-weight: 500;
}
.reservation-info-popup-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-top: 40px; /* Space for close button */
}
.popup-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}
.popup-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}
.popup-total-price {
  font-size: 16px;
  font-weight: bold;
}
.popup-total-price .price-amount {
  color: red;
  margin-left: 4px;
}
.popup-content-section {
  margin-bottom: 20px;
  max-height: calc(70vh - 180px); /* Adjust based on header/footer height */
  overflow-y: auto;
}
.details-cell-group {
  margin: 0 !important; /* Override inset margin if needed */
}
.popup-footer-buttons {
  display: flex;
  gap: 10px;
  margin-top: auto; /* Pushes buttons to the bottom if container is flex */
}
.popup-footer-buttons .van-button {
  flex: 1;
}
.custom-field-popup :deep(.van-field__label) {
  width: auto; /* Allow label to take natural width */
  margin-right: 8px;
  /* font-size: 16px; Default Vant size, will be overridden below */
}

.details-cell-group :deep(.van-field__label) {
  font-size: 18px !important;
  color: #ac1eee !important;
}
</style>
