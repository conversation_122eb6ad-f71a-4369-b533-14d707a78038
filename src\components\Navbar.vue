<template>
  <van-nav-bar
    :title="title"
    :left-text="leftText"
    :right-text="rightText"
    :style="{ backgroundColor: backgroundColor }"
    class="custom-navbar"
    @click-left="onClickLeft"
    @click-right="onClickRight"
  />
</template>

<script setup>
import { defineEmits, defineProps } from 'vue';

// 定義 props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  leftText: {
    type: String,
    default: ''
  },
  rightText: {
    type: String,
    default: ''
  },
  backgroundColor: {
    type: String,
    default: '#fff'
  }
});

// 定義 emits
const emit = defineEmits(['click-left', 'click-right']);

// 事件處理函數
const onClickLeft = () => {
  emit('click-left');
};

const onClickRight = () => {
  emit('click-right');
};
</script>

<style scoped>
.custom-navbar {
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

:deep(.van-nav-bar__title) {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

:deep(.van-nav-bar__text) {
  color: white;
  font-size: 14px;
}

:deep(.van-icon) {
  color: white !important;
}
</style>