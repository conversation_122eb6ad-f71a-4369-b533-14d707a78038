import { createRouter, createWebHistory } from 'vue-router';


const routes = [
  {
    path: '/',
    redirect: '/carorder' // 當訪問根路徑時，重定向到 /carorder
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('../views/Admin.vue'),
    redirect: '/admin/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('../components/admin/Dashboard.vue')
      },
      {
        path: 'orders',
        name: 'AdminOrderList',
        component: () => import('../components/admin/OrderList.vue')
      },
      {
        path: 'prices',
        name: 'AdminPriceManagement',
        component: () => import('../components/admin/PriceManagement.vue')
      },
      {
        path: 'dispatch',
        name: 'AdminDispatchManagement',
        component: () => import('../components/admin/DispatchManagement.vue')
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: () => import('../components/admin/Settings.vue')
      }
    ]
  },
  {
    path: '/hongyun',
    name: '<PERSON>Y<PERSON>',
    component: () => import('../components/HongYun.vue')
  },
  {
    path: '/carorder',
    name: 'CarQuotaHome',
    component: () => import('../components/CarQuota.vue'),
    // redirect: '/quota/airport-transfer',
    // children: [
    //   {
    //     path: 'quota/airport-transfer',
    //     name: 'AirportTransfer',
    //     component: () => import('../components/quota/AirportTransfer.vue')
    //   },
    //   {
    //     path: 'quota/car-tour',
    //     name: 'CarTour',
    //     component: () => import('../components/quota/CarTour.vue')
    //   },
    //   {
    //     path: 'quota/reservation',
    //     name: 'Reservation',
    //     component: () => import('../components/quota/Reservation.vue')
    //   }
    // ]
  },
  // {
  //   path: '/carorder',
  //   name: 'MyCarOrder',
  //   component: () => import('../components/CarOrder.vue')
  // },
  // {
  //   path: '/carquota/:vendor_id?',
  //   name: 'CarQuota',
  //   component: () => import('../components/CarQuota.vue'),
  //   props: true
  // },
  // 嵌套路由範例：
  // {
  //   path: '/order',
  //   name: 'OrderBase',
  //   component: () => import('../components/CarOrder.vue'), // 假設 CarOrder.vue 是包含 Tabs 的父組件
  //   children: [
  //     // ... 其他 /order 下的子路由 ...
  //     {
  //       path: 'Reservation', // 相對於父路徑 /order，所以完整路徑是 /order/Reservation
  //       name: 'OrderReservationTab',
  //       component: () => import('../components/order/Reservation.vue') 
  //     }
  //   ]
  // }
];
const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;