<template>
  <div class="airport-transfer">
    <div class="form-section">
      <van-cell-group inset>
        <div class="tabs-section">
          <div class="tabs-label">接送類別</div>
          <van-tabs v-model:active="inquiryTypeIndex" type="card" class="custom-tabs">
            <van-tab title="送機 (出國)" name="0"></van-tab>
            <van-tab title="接機 (回)" name="1"></van-tab>
          </van-tabs>
        </div>

        <div class="tabs-section">
          <div class="tabs-label">機場</div>
          <van-tabs v-model:active="airportIndex" type="card" class="custom-tabs">
            <van-tab title="桃園" name="0"></van-tab>
            <van-tab title="松山" name="1"></van-tab>
            <van-tab title="台中" name="2"></van-tab>
          </van-tabs>
        </div>

        <div class="tabs-section">
          <div class="tabs-label">車型</div>
          <van-tabs v-model:active="carTypeIndex" type="card" class="custom-tabs">
            <van-tab title="五人座" name="0"></van-tab>
            <van-tab title="五人休旅" name="1"></van-tab>
            <van-tab title="九人座" name="2"></van-tab>
          </van-tabs>
        </div>

        <van-notice-bar
          color="#1989fa"
          background="#ecf9ff"
          left-icon="info-o"
          scrollable
          :delay="1"
          class="car-info-notice"
        >
          <span style="color: red;">請先選擇區域 ;</span>
          5 人座小客車最多可載 4 人，2 件行李；
          5 人座休旅車最多可載 4 人，3 件行李；
          9 人座最多可載 7 人，6 件行李；
          兒童座椅、增高墊每加一張加100，各車型限制2；
        </van-notice-bar>

        <van-field
          v-model="area"
          required
          is-link
          readonly
          name="area"
          :label="inquiryType === '送機' ? '上車區域' : '下車區域'"
          :placeholder="inquiryType === '送機' ? '請選擇上車區域' : '請選擇下車區域'"
          class="custom-field"
          @click="showAreaPicker = true"
        />
        <van-popup v-model:show="showAreaPicker" position="bottom">
          <van-area
            title="查詢區域"
            :area-list="areaList"
            :columns-num="3"
            confirm-button-text="確認"
            cancel-button-text="取消"
            @confirm="onAreaConfirm"
            @cancel="showAreaPicker = false"
            class="custom-area-picker"
          />
        </van-popup>

        <van-field label="搭車時段" class="custom-field">
          <template #input>
            <van-radio-group v-model="rideTime" direction="horizontal" class="custom-radio-group">
              <van-radio name="day" class="custom-radio2">白天</van-radio>
              <van-radio name="night" class="custom-radio2">夜間(22~06)</van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <div class="form-section">
      <van-cell-group inset>
        <div class="compact-container">
          <div class="compact-row no-border">
            <div class="inline-cell">
              <span class="inline-label">乘客人數</span>
              <van-stepper v-model="passengerCount" :min="1" :max="getMaxPassenger" theme="round" button-size="20" class="compact-stepper" />
            </div>
            <div class="inline-cell">
              <span class="inline-label inline-label-spaced">行李數</span>
              <van-stepper v-model="luggageCount" :min="0" :max="getMaxLuggage" theme="round" button-size="20" class="compact-stepper" />
            </div>
          </div>

          <div class="compact-row no-padding-top">
            <div class="inline-cell">
              <span class="inline-label">兒童座椅</span>
              <van-stepper v-model="childSeatCount" :min="0" :max="2" theme="round" button-size="20" class="compact-stepper" />
            </div>
            <div class="inline-cell">
              <span class="inline-label inline-label-spaced">增高墊</span>
              <van-stepper v-model="boosterSeatCount" :min="0" :max="2" theme="round" button-size="20" class="compact-stepper" />
            </div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <div class="form-section">
      <van-cell-group inset>
        <van-field label="其他資訊" class="custom-field checkbox-field">
          <template #input>
            <div class="checkbox-container">
              <van-checkbox-group
                v-model="otherInfo"
                class="custom-checkbox-group"
                direction="horizontal"
                :disabled="isOtherInfoDisabled"
              >
                <van-checkbox name="舉牌" class="custom-checkbox" shape="square" :disabled="isOtherInfoDisabled">舉牌</van-checkbox>
                <van-checkbox name="增加停靠點" class="custom-checkbox custom-checkbox-stoppoint" shape="square" :disabled="isOtherInfoDisabled">
                  增加停靠點
                  <van-button
                    v-if="otherInfo.includes('增加停靠點') && stopAddresses.length > 0 && !isOtherInfoDisabled"
                    icon="edit"
                    type="primary"
                    size="mini"
                    class="edit-stop-points-button-inline"
                    @click.stop="editStopPoints"
                  >
                    編輯
                  </van-button>
                </van-checkbox>
              </van-checkbox-group>

              <div v-if="stopAddresses.length > 0" class="stop-address-tags">
                <van-tag
                  v-for="(address, index) in stopAddresses"
                  :key="index"
                  type="primary"
                  closeable
                  size="medium"
                  class="stop-address-tag"
                  @close="removeStopAddressTag(index)"
                >
                  停靠點 {{ index + 1 }}: {{ address }}
                </van-tag>
              </div>
            </div>
          </template>
        </van-field>

        <van-notice-bar
          v-if="otherInfo.includes('舉牌')"
          color="#ff6b00"
          background="#fff7e8"
          left-icon="info-o"
          scrollable
          :delay="1"
          class="service-info-notice"
        >
          {{ getServiceNoticeText() }}
        </van-notice-bar>

        <van-field
          v-model="notes"
          name="notesQuotaAirport"
          label="備註說明"
          rows="2"
          autosize
          type="textarea"
          maxlength="100"
          placeholder="請輸入註記事項"
          show-word-limit
          class="custom-field custom-textarea"
        />
      </van-cell-group>
    </div>

    <div class="submit-area">
      <van-submit-bar
        :price="totalPrice * 100"
        button-text="我要預約"
        label="合計:"
        @submit="showReservationPopup"
        currency="NT$"
        :disabled="!area || totalPrice <= 0"
        decimal-length="1"
      />
    </div>

    <!-- 日期選擇彈窗 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-picker-group
        title="選擇日期和時間"
        :tabs="['選擇日期', '選擇時間']"
        class="custom-area-picker"
        @confirm="onPickerConfirm"
        @cancel="showDatePicker = false"
        confirm-button-text="確認"
        cancel-button-text="取消"
      >
        <van-date-picker
          v-model="currentDate"
          :min-date="minDate"
          :max-date="maxDate"
        />
        <van-time-picker
          v-model="currentTime"
          :filter="timeFilter"
        />
      </van-picker-group>
    </van-popup>

    <!-- 停靠點編輯彈窗 -->
    <van-popup
      v-model:show="showStopPointsPopup"
      position="bottom"
      round
      :style="{ height: '60%' }"
      class="stop-points-popup"
      @closed="onPopupClosed"
    >
      <div class="stop-points-container">
        <div class="stop-points-header">
          <div class="header-top">
            <van-button
              icon="plus"
              type="primary"
              size="small"
              class="add-button-top"
              @click="addStopAddress"
            >
              新增停靠點
            </van-button>
            <van-icon
              name="cross"
              class="close-icon"
              @click="showStopPointsPopup = false"
            />
          </div>
          <p class="stop-points-subtitle">請添加您需要的停靠點地址</p>
        </div>

        <div class="stop-points-list">
          <div v-for="(address, index) in tempStopAddresses" :key="index" class="stop-point-item">
            <van-field
              v-model="tempStopAddresses[index]"
              placeholder="請輸入地址"
              class="stop-point-input"
            />
            <van-button
              icon="delete"
              type="danger"
              size="small"
              class="delete-button"
              @click="removeStopAddress(index)"
            />
          </div>

          <div class="close-button-container">
            <van-button
              icon="cross"
              type="success"
              size="small"
              class="close-button"
              @click="showStopPointsPopup = false"
            >
              儲存並關閉
            </van-button>
          </div>
        </div>

        <div class="stop-points-footer">
          <van-button
            type="primary"
            block
            round
            class="confirm-button"
            @click="confirmStopPoints"
          >
            確認
          </van-button>
        </div>
      </div>
    </van-popup>

        <!-- 使用可重複使用的預約資訊彈窗 -->
    <ReservationInfoPopup
      v-model:show="showReservationDialog"
      :total-price="totalPrice"
      @confirm="submitReservation"
      :confirm-loading="isSubmitting"
      v-model:appointmentDateModel="reservationDate"
      v-model:flightNumberModel="flightNumber"
      v-model:passengerNameModel="passengerName"
      v-model:contactPhoneModel="contactPhone"
      v-model:pickupAddressModel="pickupAddress"
      v-model:payTypeModel="payType"
      :inquiryType="inquiryType"
      title="請填寫預約資訊"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { showToast, showLoadingToast, closeToast, showDialog } from 'vant';
import { useRouter } from 'vue-router';
import ReservationInfoPopup from '../shared/ReservationInfoPopup.vue'; // 引入新元件
import liff from '@line/liff';

// LIFF 相關
const lineId = ref('');
const liffId = import.meta.env.VITE_LIFF_ID;

// 初始化 LIFF
const initializeLiff = async () => {
  try {
    await liff.init({ liffId });
    if (!liff.isLoggedIn()) {
      liff.login();
    } else {
      const profile = await liff.getProfile();
      lineId.value = profile.userId;
    }
  } catch (e) {
    console.error(e);
    showToast('LIFF 初始化失敗');
  }
};


// 提交狀態
const isSubmitting = ref(false);
// 定義固定的 vendor_id
const vendor_id = ref(10);
const API_BASE_URL = import.meta.env.VITE_API_SERVER;

// 機場代碼映射
const airportCodeMap = {
  '桃園機場': '110903',
  '松山機場': '110900',
  '台中機場': '120901'
};
// 機場代碼映射
const carTypeMap = {
  '五人座': '5',
  '五人休旅': '51',
  '九人座': '9'
};

// 定義響應式狀態
const inquiryType = ref('送機');
const inquiryTypeIndex = ref('0'); // 對應 tabs 的 index
const airport = ref('桃園機場');
const airportIndex = ref('0'); // 對應 tabs 的 index
const airportCode = ref(airportCodeMap['桃園機場']); // 默認桃園機場代碼
const carType = ref('五人座');
const carTypeIndex = ref('0'); // 對應 tabs 的 index
const passengerCount = ref(1);
const luggageCount = ref(0);
const childSeatCount = ref(0);
const boosterSeatCount = ref(0);
const otherInfo = ref([]);
const notes = ref('');
const area = ref('');
const rideTime = ref('day'); // 新增搭乘時段狀態

// 停靠點相關
const stopAddresses = ref([]);
const showStopPointsPopup = ref(false);
const tempStopAddresses = ref([]);

// 新增彈窗相關狀態
const showReservationDialog = ref(false);
const showDatePicker = ref(false);
const reservationDate = ref('');
const flightNumber = ref('');
const passengerName = ref('');
const contactPhone = ref('');
const pickupAddress = ref('');
const payType = ref('現金'); // 默認現金支付 (0:現金, 1:信用卡)

// 日期選擇相關
const today = new Date();
const currentDate = ref([
  String(today.getFullYear()),
  String(today.getMonth() + 1).padStart(2, '0'),
  String(today.getDate()).padStart(2, '0')
]);
const currentTime = ref(['12', '00']);
const minDate = new Date();
const maxDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1));
const columnsType = ['year', 'month', 'day', 'hour', 'minute'];

// 時間過濾器
const timeFilter = (type, options) => {
  if (type === 'minute') {
    return options.filter(option => Number(option.value) % 5 === 0);
  }
  return options;
};

// Picker Group 確認處理
const onPickerConfirm = ({ selectedValues }) => {
  const formattedDate = currentDate.value.join('-');
  const formattedTime = currentTime.value.join(':');
  reservationDate.value = `${formattedDate} ${formattedTime}`;
  showDatePicker.value = false;
};

// 價格相關
const basicPrice = ref(0);
const additionalPrice = ref(0);
const totalPrice = ref(0);
const router = useRouter();

// 計算屬性，判斷“其他資訊”部分是否應禁用
const isOtherInfoDisabled = computed(() => !area.value);

// 服務項目名稱到代碼的映射
const ADDITIONAL_CHARGES = {
  sign: 100, // 舉牌
  nightSurcharge: 100, // 夜間加成
  childSeat: 100, // 兒童座椅
  boosterSeat: 100, // 增高墊
  stopAddresses: 100 // 停靠站
};

const serviceToCodeMap = {
  '舉牌': '1',
  '夜間加成': '2',
  '增加停靠點': '3'
};

// 單一且正確定義的區域數據獲取函數
const fetchCityData = async () => {
  const loadingToast = showLoadingToast({
    message: '加載區域數據...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const response = await fetch(`${API_BASE_URL}/api/getordercitys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        vendor_id:vendor_id.value,
        airport_code: airportCode.value,
        vehicle_type_name: carTypeMap[carType.value]
      })
    });
    const result = await response.json();

    if (result.success) {
      areaList.value = {
        province_list: result.data.province_list,
        city_list: result.data.city_list,
        county_list: result.data.county_list
      };
      loadingToast.close();
      showToast('請選擇區域');
    } else {
      loadingToast.close();
      showToast(result.message || '獲取區域數據失敗');
    }
  } catch (error) {
    loadingToast.close();
    showToast('網路錯誤，無法獲取區域數據');
    console.error('API請求失敗:', error);
  }
};

// 區域選擇相關
const showAreaPicker = ref(false);
const areaList = ref({
  province_list: {},
  city_list: {},
  county_list: {}
});

// 同步 tabs 選擇和實際值
const inquiryTypeOptions = ['送機', '接機'];
const airportOptions = ['桃園機場', '松山機場', '台中機場'];
const carTypeOptions = ['五人座', '五人休旅', '九人座'];

// 監聽 tabs 變化，更新實際值
watch(inquiryTypeIndex, (newIndex) => {
  inquiryType.value = inquiryTypeOptions[parseInt(newIndex)];
});

watch(airportIndex, (newIndex) => {
  airport.value = airportOptions[parseInt(newIndex)];
});

watch(carTypeIndex, (newIndex) => {
  carType.value = carTypeOptions[parseInt(newIndex)];
});

// 監聽實際值變化，更新 tabs 索引
watch(inquiryType, (newValue) => {
  const index = inquiryTypeOptions.indexOf(newValue);
  if (index !== -1) inquiryTypeIndex.value = index.toString();
});

watch(airport, (newValue) => {
  const index = airportOptions.indexOf(newValue);
  if (index !== -1) airportIndex.value = index.toString();
});

watch(carType, (newValue) => {
  const index = carTypeOptions.indexOf(newValue);
  if (index !== -1) carTypeIndex.value = index.toString();
});

// 監聽接送類別、機場變化，更新機場代碼但不重新加載區域數據
watch([inquiryType, airport], (newValues) => {
  // 更新機場代碼
  airportCode.value = airportCodeMap[newValues[1]] || '110903';
});

// 監聽其他資訊變化
watch(otherInfo, (newValue, oldValue) => {
  const hasStopPoint = newValue.includes('增加停靠點');
  const hadStopPoint = oldValue && oldValue.includes('增加停靠點');

  if (hasStopPoint && !hadStopPoint) {
    if (stopAddresses.value.length === 0) {
      tempStopAddresses.value = [''];
    } else {
      tempStopAddresses.value = [...stopAddresses.value];
    }
    showStopPointsPopup.value = true;
  } else if (!hasStopPoint && hadStopPoint) {
    // 如果取消勾選“增加停靠點”，清空相關數據
    stopAddresses.value = [];
    tempStopAddresses.value = [];
  }
  // 價格計算將由下面的全局 watcher 處理
});

// 添加停靠點
const addStopAddress = () => {
  tempStopAddresses.value.push('');
};
// 編輯停靠點
const editStopPoints = () => {
  // 使用現有的停靠點填充臨時列表以供編輯
  if (stopAddresses.value.length > 0) {
    tempStopAddresses.value = [...stopAddresses.value];
  } else {
    // 理論上，如果 stopAddresses 為空，編輯按鈕不應顯示
    // 作為備用，如果用戶點擊編輯時沒有停靠點，則提供一個空欄位
    tempStopAddresses.value = [''];
  }
  showStopPointsPopup.value = true;
};
// 移除停靠點 (編輯彈窗中)
const removeStopAddress = (index) => {
  tempStopAddresses.value.splice(index, 1);
  if (tempStopAddresses.value.length === 0) {
    const checkboxIndex = otherInfo.value.indexOf('增加停靠點');
    if (checkboxIndex !== -1) {
      otherInfo.value.splice(checkboxIndex, 1);
    }
    showStopPointsPopup.value = false;
  }
};

// 移除停靠點標籤
const removeStopAddressTag = (index) => {
  stopAddresses.value.splice(index, 1);
  if (stopAddresses.value.length === 0) {
    const checkboxIndex = otherInfo.value.indexOf('增加停靠點');
    if (checkboxIndex !== -1) {
      otherInfo.value.splice(checkboxIndex, 1);
    }
  }
};

// 確認停靠點
const confirmStopPoints = () => {
  const filteredAddresses = tempStopAddresses.value.filter(address => address.trim() !== '');
  stopAddresses.value = [...filteredAddresses];
  if (stopAddresses.value.length === 0) {
    const checkboxIndex = otherInfo.value.indexOf('增加停靠點');
    if (checkboxIndex !== -1) {
      otherInfo.value.splice(checkboxIndex, 1);
    }
  }
  showStopPointsPopup.value = false;
};

// 彈窗關閉處理
const onPopupClosed = () => {
  const filteredAddresses = tempStopAddresses.value.filter(address => address.trim() !== '');
  if (filteredAddresses.length > 0) {
    stopAddresses.value = [...filteredAddresses];
    if (!otherInfo.value.includes('增加停靠點')) {
      otherInfo.value.push('增加停靠點');
    }
  } else {
    const checkboxIndex = otherInfo.value.indexOf('增加停靠點');
    if (checkboxIndex !== -1) {
      otherInfo.value.splice(checkboxIndex, 1);
    }
    stopAddresses.value = [];
  }
};
// 區域 ID 相關
const selectedAreaIds = ref({
  province_id: '',
  city_id: '',
  district_id: ''
});
// 獲取報價
const fetchPrice = async () => {
  if (!area.value) {
    basicPrice.value = 0;
    additionalPrice.value = 0;
    totalPrice.value = 0;
    return;
  }

  try {
    showLoadingToast({
      message: '計算價格中...',
      forbidClick: true,
      duration: 0
    });

    const currentAirportCode = airportCodeMap[airport.value] || '110903';
    const orderTypeForApi = inquiryType.value === '接機' ? '1' : '0';
    const params = {
      order_type: orderTypeForApi,
      airport: currentAirportCode,
      car_type: carTypeMap[carType.value],
      tour_type: 1,
      // Convert service names to codes for the pricing API
      other_service: otherInfo.value.map(serviceName => serviceToCodeMap[serviceName]).filter(Boolean),
      vendor_id: vendor_id.value,
      quo_type: 0,
      num_of_people: passengerCount.value.toString(),
      num_of_bags: luggageCount.value.toString(),
      child_seat: childSeatCount.value.toString(),
      booster_pad: boosterSeatCount.value.toString(),
      line_id: lineId.value,
      note: notes.value,
      lang: 'zh-TW',
      to_city_id: selectedAreaIds.value.city_id || '110500',
      to_district_id: selectedAreaIds.value.district_id || '110501',
      to_area_id: selectedAreaIds.value.province_id || '110000',
      stop_addresses: stopAddresses.value
    };

    const response = await fetch(`${API_BASE_URL}/api/price`, { // Changed to /api/price, confirm your actual pricing endpoint
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    const result = await response.json();
    closeToast();

    let apiBasicPrice = 0;
    let apiAdditionalPrice = 0; // API 返回的附加服務費
    // let apiTotalPrice = 0; // API 計算的總價
    let priceSuccessfullyParsed = false;

    const safeParse = (val, def = 0) => {
      const num = Number(val);
      return isNaN(num) ? def : num;
    };

    if (result.success) {
      if (result.data && result.data.calculated_price !== undefined) {
        // apiTotalPrice = safeParse(result.data.calculated_price);
        apiBasicPrice = safeParse(result.data.basic_price, safeParse(result.data.calculated_price)); // 如果 basic_price 未提供，嘗試使用 calculated_price
        apiAdditionalPrice = safeParse(result.data.additional_service_price);
        priceSuccessfullyParsed = true;
      } else if (result.calculated_price !== undefined) {
        // apiTotalPrice = safeParse(result.calculated_price);
        apiBasicPrice = safeParse(result.basic_price, safeParse(result.calculated_price));
        apiAdditionalPrice = safeParse(result.additional_service_price);
        priceSuccessfullyParsed = true;
      } else if (result.data && result.data.price !== undefined) { // API 只返回總價
        apiBasicPrice = safeParse(result.data.price);
        apiAdditionalPrice = 0; // 假設 API 的附加費為0
        priceSuccessfullyParsed = true;
      } else if (result.price !== undefined) { // API 只返回總價
        apiBasicPrice = safeParse(result.price);
        apiAdditionalPrice = 0;
        priceSuccessfullyParsed = true;
      }

      if (priceSuccessfullyParsed) {
        basicPrice.value = apiBasicPrice;
        additionalPrice.value = apiAdditionalPrice;
        updateTotalPrice();
      } else {
        showToast('無法解析價格數據');
        basicPrice.value = 0; additionalPrice.value = 0; totalPrice.value = 0;
      }
    } else {
      showDialog({
        title: '查詢失敗',
        message: '未查到該地區價格，請與客服聯繫。',
        theme: 'round-button',
      });
      basicPrice.value = 0;
      additionalPrice.value = 0;
      totalPrice.value = 0;
    }
  } catch (error) {
    closeToast();
    basicPrice.value = 0;
    additionalPrice.value = 0;
    totalPrice.value = 0;
    showToast('網絡錯誤，請稍後再試');
  }
};

// 顯示預約資訊彈窗
const showReservationPopup = () => {
  if (!area.value) {
    showToast('請選擇區域');
    return;
  }
  showReservationDialog.value = true;
};

const submitReservation = async () => {
  console.log('[quota/AirportTransfer] submitReservation triggered. Date:', reservationDate.value);
  // 重新進行所有欄位的嚴格驗證
  if (!reservationDate.value) {
    showToast('請選擇預約日期和時間');
    return;
  }

  // 無論是接機、送機或來回，都應該驗證航班編號
  // 但如果是 '--' 表示用戶選擇了「不知道」，這是有效的
  if (!flightNumber.value || flightNumber.value.trim() === '') {
    showToast('請輸入有效的航班編號');
    return;
  }

  if (!passengerName.value || !passengerName.value.trim()) {
    showToast('請輸入訂車人姓名');
    return;
  }
  if (!contactPhone.value || !contactPhone.value.trim()) {
    showToast('請輸入聯絡電話');
    return;
  }
  if (!pickupAddress.value || !pickupAddress.value.trim()) {
    showToast('請輸入詳細地址');
    return;
  }

  isSubmitting.value = true;
  try {

    const inquiryTypeMap = {
      '送機': '0',
      '接機': '1',
      '來回': '4'
    };
    // 構建提交到後端的訂單數據
    const payload = {
      type: '機場接送', // 服務總類型
      order_type: inquiryTypeMap[inquiryType.value], // 0:送機, 1:接機, 2:來回
      airport: airportCode.value, // 機場代碼
      car_type: carTypeMap[carType.value], // 車型代碼
      
      area_name: area.value, // 區域名稱 (例如：台北市 中正區)
      to_area_id: selectedAreaIds.value.province_id, // 省/直轄市 ID
      to_city_id: selectedAreaIds.value.city_id,     // 城市 ID
      to_district_id: selectedAreaIds.value.district_id, // 區 ID

      num_of_people: passengerCount.value.toString(),
      num_of_bags: luggageCount.value.toString(),
      child_seat: childSeatCount.value.toString(),
      booster_pad: boosterSeatCount.value.toString(),
      
      // 將服務項目文字轉為代碼
      other_service: otherInfo.value.map(serviceName => serviceToCodeMap[serviceName]).filter(Boolean),
      stop_addresses: stopAddresses.value,
      
      appointment_date: reservationDate.value, // 預約日期時間
      flightno: flightNumber.value,
      passenger_name: passengerName.value,
      passenger_mobile: contactPhone.value,
      passenger_address: pickupAddress.value, // 詳細接送地址
      
      total_price: totalPrice.value,
      vendor_id: vendor_id.value,
      line_id: lineId.value, // 根據實際情況修改
      notes: notes.value, // 備註
      lang: 'zh-TW',
      pay_type: payType.value
    };

    console.log('提交訂單數據:', payload);

    // 實際的 API 調用
    // 請確認您的後端 API 端點是否為 /api/orders
    const response = await fetch(`${API_BASE_URL}/api/carorder/v3`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();
    closeToast();

    if (response.ok && result.success) {
      await liff.sendMessages([
        {
          type: 'text',
          text: result.data.result || '您的預約已成功！'
        }
      ]);
      liff.closeWindow();
    } else {
      throw new Error(result.message || '預約失敗，請稍後再試');
    }
  } catch (error) {
    closeToast();
    isSubmitting.value = false;
    showToast({
      type: 'fail',
      message: error.message || '預約過程中發生錯誤',
      duration: 2000
    });
  }
    // isSubmitting.value = false; // 確保在 try-catch 外部也設置，或在 finally 中

};

// 清空表單
const resetForm = () => {
  inquiryType.value = '送機';
  airport.value = '桃園機場';
  carType.value = '5';
  passengerCount.value = 1;
  luggageCount.value = 0;
  childSeatCount.value = 0;
  boosterSeatCount.value = 0;
  otherInfo.value = [];
  area.value = '';
  selectedAreaIds.value = { province_id: '', city_id: '', district_id: '' }; // 重置區域ID
  reservationDate.value = '';
  flightNumber.value = '';
  passengerName.value = '';
  contactPhone.value = '';
  pickupAddress.value = '';
  payType.value = '現金'; // 重置付款方式
  notes.value = ''; // 清空備註
  stopAddresses.value = []; // 清空停靠點
  // totalPrice 等價格相關的 ref 會在 area.value 為空時由 fetchPrice 重置
};

// 區域確認處理
const onAreaConfirm = (values) => {
  if (values.selectedOptions) { // Vant 4.x 格式
    const [province, city, county] = values.selectedOptions;
    area.value = `${province.text} ${city.text} ${county.text}`;
    selectedAreaIds.value = {
      province_id: province.value,
      city_id: city.value,
      district_id: county.value
    };
  } else if (Array.isArray(values)) { // Vant 3.x 格式
    const [province, city, county] = values;
    area.value = `${province.name} ${city.name} ${county.name}`;
    selectedAreaIds.value = {
      province_id: province.code,
      city_id: city.code,
      district_id: county.code
    };
    
  } else if (values && typeof values === 'object') {
    const selectedValues = values.selectedOptions || values.values;
    if (selectedValues && Array.isArray(selectedValues)) {
      const names = [];
      for (const item of selectedValues) {
        if (item && (item.text || item.name)) {
          names.push(item.text || item.name);
        }
      }
      area.value = names.join(' ');
    }
  }
  showAreaPicker.value = false;
  fetchPrice();
};

// 更新總價格的方法
const updateTotalPrice = () => {
  let surcharge = 0;
  if (rideTime.value === 'night') surcharge += ADDITIONAL_CHARGES.nightSurcharge;
  if (otherInfo.value.includes('舉牌')) surcharge += ADDITIONAL_CHARGES.sign;

  if (inquiryType.value === '來回' && basicPrice.value > 0) {
    surcharge += (childSeatCount.value * ADDITIONAL_CHARGES.childSeat) * 2;
    surcharge += (boosterSeatCount.value * ADDITIONAL_CHARGES.boosterSeat) * 2;
    surcharge += (stopAddresses.value.length * ADDITIONAL_CHARGES.stopAddresses) * 2;
    const roundTripBasePrice = (basicPrice.value * 2) + 100;
    totalPrice.value = roundTripBasePrice + additionalPrice.value + surcharge;
  } else {
    surcharge += childSeatCount.value * ADDITIONAL_CHARGES.childSeat;
    surcharge += boosterSeatCount.value * ADDITIONAL_CHARGES.boosterSeat;
    surcharge += stopAddresses.value.length * ADDITIONAL_CHARGES.stopAddresses;
    totalPrice.value = basicPrice.value + additionalPrice.value + surcharge;
  }
};

// 監聽搭乘時段、舉牌、加點等異動
watch([
  rideTime,
  otherInfo,
  childSeatCount,
  boosterSeatCount,
  stopAddresses
], updateTotalPrice, { deep: true });

// 監聽所有影響價格的表單欄位變化
watch(
  [inquiryType, airport, carType, area, passengerCount, luggageCount, childSeatCount, boosterSeatCount, otherInfo, stopAddresses],
  () => {
    if (area.value) { // 只有在選擇了區域後才計算價格
      fetchPrice();
    } else {
      // 如果區域未選擇（例如被清空），則重置價格
      basicPrice.value = 0;
      additionalPrice.value = 0;
      totalPrice.value = 0;
    }
  },
  { deep: true } // 對 otherInfo 和 stopAddresses 數組進行深度監聽
);


// 獲取服務提示文本
const getServiceNoticeText = () => {
  const notices = [];
  if (otherInfo.value.includes('舉牌')) {
    notices.push('舉牌服務需加收100元');
  }
  return notices.join('；');
};

// 車型對應最大人數與行李數
const carTypeLimits = {
  '五人座': { maxPassenger: 4, maxLuggage: 2 },
  '五人休旅': { maxPassenger: 4, maxLuggage: 3 },
  '九人座': { maxPassenger: 7, maxLuggage: 6 },
};

const getMaxPassenger = computed(() => carTypeLimits[carType.value]?.maxPassenger || 4);
const getMaxLuggage = computed(() => carTypeLimits[carType.value]?.maxLuggage || 2);

onMounted(() => {
  initializeLiff();
  fetchCityData(); // 正確調用已定義的函數
  fetchPrice(); // 初始化時獲取價格
});

// 監聽搭乘時段變化，夜間加收 100 元
</script>

<style scoped>
.custom-area-picker :deep(.van-picker__toolbar) {
  background-color: #f2f399;
}
.custom-area-picker :deep(.van-picker__columns) {
  background-color: #ececbe;
}

.custom-area-picker :deep(.van-picker__title) {
  color: #4CAF50;
  font-weight: 600;
}

.custom-area-picker :deep(.van-picker__confirm) {
  color: #4CAF50;
}

.custom-area-picker :deep(.van-picker-column__item--selected) {
  color: #4CAF50;
  font-weight: 500;
}
.custom-field :deep(.van-field__label) {
  font-size: 16px !important;
  color: #ac1eee !important;
  font-weight: 500;
  width: 80px !important;
}

.custom-textarea {
  /* 您可以為文字區域添加特定的背景色或邊框 */
}

.custom-textarea :deep(.van-field__control) {
  min-height: 48px; /* 確保至少有兩行的高度 */
  font-size: 14px;
  padding: 8px;
  background-color: #f7f8fa; /* 淺灰色背景，使其看起來像輸入框 */
  border-radius: 4px;
}
.airport-transfer {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 12px;
  padding-bottom: 12px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

@media (max-width: 480px) {
  .airport-transfer {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.form-section {
  margin-bottom: 12px;
}

:deep(.van-cell-group--inset) {
  margin: 0 8px 12px;
  border-radius: 16px;
  overflow: hidden;
}

.custom-cell {
  padding: 12px 16px;
}

.custom-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-radio {
  margin-right: 12px;
}
.custom-radio2 {
  font-size: 14px;
}

/* Tabs 樣式 */
.tabs-section {
  margin-bottom: 16px;
}

.tabs-label {
  font-size: 14px;
  color: #323233;
  margin-bottom: 8px;
  font-weight: 500;
}

.custom-tabs {
  --van-tabs-card-height: 36px;
}

.custom-tabs .van-tab {
  font-size: 14px;
}

.custom-tabs .van-tab--active {
  background-color: #1989fa;
  color: white;
}

.custom-field {
  border-radius: 8px;
  margin-bottom: 8px;
}

.car-info-notice {
  margin: 8px 0;
  border-radius: 8px;
}

.service-info-notice {
  margin-top: 8px;
  border-radius: 8px;
}

.compact-container {
  padding: 0 16px;
}

.compact-row {
  display: flex;
  justify-content: space-between;
  padding-bottom: 12px;
}

.no-border {
  border-bottom: none;
}

.no-padding-top {
  padding-top: 0;
}

.inline-cell {
  display: flex;
  align-items: center;
  flex: 1;
}

.inline-label {
  margin-right: 8px;
  margin-left: 4px;
  font-size: 16px;
  color: #ac1eee;
}
.inline-label-spaced {
  margin-right: 6px;
  padding-left: 6px
}

.compact-stepper {
  margin-left: auto;
}

.checkbox-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.custom-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-checkbox {
  margin-right: 12px;
}

.stop-address-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.stop-address-tag {
  margin-right: 8px;
}

.submit-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.stop-points-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.stop-points-header {
  margin-bottom: 20px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stop-points-subtitle {
  font-size: 16px;
  color: #888;
}

.stop-points-list {
  flex: 1;
  overflow-y: auto;
}

.stop-point-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.stop-point-input {
  flex: 1;
  margin-right: 10px;
}

.delete-button {
  flex-shrink: 0;
}

.close-button-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.stop-points-footer {
  /* margin-top: 20px; */
  margin-bottom: 30px;
}

.reservation-info {
  display: flex; /* 使用 Flexbox 佈局 */
  justify-content: flex-start; /* 標題在左，總價在右 */
  align-items: center; /* 垂直居中對齊 */
  padding: 12px 16px; /* 調整內邊距 */
  border-bottom: 1px solid #eee; /* 底部加上分隔線 */
}

.reservation-info .popup-total-price-display {
  font-size: 18px; /* 讓總價文字更突出 */
  font-weight: bold; /* 加粗 */
  margin-left: 16px;
}
.reservation-info .popup-total-price-display .price-amount-highlight {
  color: red; /* 價格數值使用紅色 */
  margin-right: 4px; /* 在 "合計:" 和價格之間增加少許間距 */
}
.custom-radio :deep(.van-radio__label) {
  font-size: 16px !important; /* 您可以將 'large' 替換為具體的像素值，如 16px */
}
.custom-checkbox :deep(.van-checkbox__label) {
  font-size: 16px !important;
}
/* 調整 custom-cell 內 van-cell 標題的字體大小和顏色 */
.custom-cell :deep(.van-cell__title) {
  font-size: 16px !important;
  color: #ac1eee !important;
}

/* 調整小螢幕的 label 和 input 寬度，避免跑版 */
@media screen and (max-width: 480px) {
  .custom-field :deep(.van-field__label) {
    width: 80px !important;
  }

  .custom-field :deep(.van-field__control) {
    max-width: calc(100% - 10px);
  }
}
</style>
