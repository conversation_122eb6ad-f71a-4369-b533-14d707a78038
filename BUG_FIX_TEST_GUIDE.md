# 表單驗證 Bug 修復測試指南

## 🐛 修復的問題

### 問題 1: 已經輸入資料，還有紅色字體
**原因**: 輸入欄位的 `@update:model-value` 事件沒有清除錯誤狀態

### 問題 2: 航班勾選"不知道"無法送出
**原因**: 驗證邏輯沒有正確處理 `flightNumberModel` 為 `'--'` 的情況

## 🔧 修復內容

### 1. 改進輸入欄位錯誤狀態清除
**修改前**:
```javascript
@update:model-value="$emit('update:flightNumberModel', $event)"
```

**修改後**:
```javascript
@update:model-value="(value) => { clearFieldError('flightNumber'); $emit('update:flightNumberModel', value); }"
```

### 2. 修正航班編號驗證邏輯
**修改前**:
```javascript
if (shouldShowFlightNumber.value && !flightNumberUnknown.value) {
  if (!props.flightNumberModel || props.flightNumberModel.trim() === '') {
    // 驗證失敗
  }
}
```

**修改後**:
```javascript
if (shouldShowFlightNumber.value) {
  if (!flightNumberUnknown.value) {
    if (!props.flightNumberModel || props.flightNumberModel.trim() === '' || props.flightNumberModel.trim() === '--') {
      // 驗證失敗
    }
  } else if (props.flightNumberModel !== '--') {
    emit('update:flightNumberModel', '--');
  }
}
```

### 3. 添加雙向同步機制
```javascript
// 監聽 flightNumberModel 的變化，同步 flightNumberUnknown 狀態
watch(() => props.flightNumberModel, (newVal) => {
  if (newVal === '--') {
    flightNumberUnknown.value = true;
  } else if (flightNumberUnknown.value && newVal !== '--') {
    flightNumberUnknown.value = false;
  }
});
```

## 🧪 測試步驟

### 測試 1: 輸入欄位錯誤狀態清除

#### 步驟：
1. 打開 http://127.0.0.1:5174/
2. 選擇「機場接送」標籤
3. 選擇接送類型和區域
4. 點擊「查詢價格」
5. 點擊「預約」按鈕
6. **直接點擊「提交訂單」**（不填寫任何資料）
7. 觀察所有欄位都顯示紅色邊框和錯誤訊息
8. **開始在「訂車人大名」欄位輸入文字**
9. 觀察該欄位的紅色邊框是否立即消失

#### 預期結果：
- ✅ 輸入時紅色邊框立即消失
- ✅ 錯誤訊息立即消失
- ✅ 其他未填寫欄位仍保持紅色狀態

#### 測試所有欄位：
- [ ] 訂車人大名
- [ ] 連絡電話
- [ ] 上車/下車地址
- [ ] 班機編號（如果顯示）

### 測試 2: 航班編號「不知道」功能

#### 步驟：
1. 在機場接送表單中
2. 確保顯示班機編號欄位（選擇送機/接機/來回）
3. **勾選「不知道」選項**
4. 觀察班機編號欄位是否變為禁用狀態
5. 觀察班機編號欄位值是否變為 `--`
6. 填寫其他必填欄位（日期、姓名、電話、地址、付款方式）
7. **點擊「提交訂單」**

#### 預期結果：
- ✅ 勾選「不知道」後，班機編號欄位禁用
- ✅ 班機編號值自動設為 `--`
- ✅ 表單可以成功提交（不會因班機編號驗證失敗）
- ✅ 不會顯示班機編號相關錯誤訊息

### 測試 3: 航班編號取消「不知道」

#### 步驟：
1. 先勾選「不知道」選項
2. 觀察班機編號變為 `--`
3. **取消勾選「不知道」**
4. 觀察班機編號欄位是否重新啟用
5. 觀察班機編號值是否清空
6. 不輸入班機編號，直接提交表單

#### 預期結果：
- ✅ 取消勾選後，班機編號欄位重新啟用
- ✅ 班機編號值自動清空
- ✅ 提交時會顯示班機編號驗證錯誤

### 測試 4: 綜合測試

#### 步驟：
1. 觸發所有欄位的驗證錯誤
2. 逐一填寫每個欄位
3. 觀察每個欄位的錯誤狀態是否在輸入時立即清除
4. 測試航班編號的「不知道」功能
5. 最終提交完整表單

#### 預期結果：
- ✅ 所有欄位的錯誤狀態都能正確清除
- ✅ 航班編號「不知道」功能正常
- ✅ 完整表單可以成功提交

## 🔍 除錯提示

### 如果輸入後仍有紅色字體：
1. 檢查瀏覽器控制台是否有 JavaScript 錯誤
2. 檢查 `clearFieldError` 函數是否正確執行
3. 檢查 Vue DevTools 中的 `fieldErrors` 狀態

### 如果航班「不知道」仍無法提交：
1. 檢查 `flightNumberUnknown` 的值
2. 檢查 `props.flightNumberModel` 的值
3. 檢查驗證邏輯中的條件判斷

### 檢查方法：
```javascript
// 在瀏覽器控制台中執行
console.log('flightNumberUnknown:', flightNumberUnknown.value);
console.log('flightNumberModel:', props.flightNumberModel);
console.log('fieldErrors:', fieldErrors.value);
```

## ✅ 驗證清單

### 基本功能
- [ ] 表單驗證錯誤正常顯示
- [ ] Toast 提示正常顯示
- [ ] 欄位紅色邊框正常顯示

### 錯誤狀態清除
- [ ] 訂車人大名輸入時清除錯誤
- [ ] 連絡電話輸入時清除錯誤
- [ ] 地址輸入時清除錯誤
- [ ] 班機編號輸入時清除錯誤
- [ ] 日期選擇時清除錯誤
- [ ] 付款方式選擇時清除錯誤

### 航班編號功能
- [ ] 勾選「不知道」後欄位禁用
- [ ] 勾選「不知道」後值變為 `--`
- [ ] 勾選「不知道」後可以提交表單
- [ ] 取消勾選後欄位重新啟用
- [ ] 取消勾選後值清空
- [ ] 狀態同步正確

### 整體測試
- [ ] 所有服務類型都正常（送機/接機/來回/兩地接送/包車）
- [ ] 不同瀏覽器都正常工作
- [ ] 無 JavaScript 錯誤

---

**測試完成後，這兩個 Bug 應該都已修復！** 🎉
