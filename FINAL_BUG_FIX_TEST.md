# 最終 Bug 修復測試指南

## 🐛 修復的問題

### 問題 1: 勾了航班"不知道"還是無法驗證過
**根本原因**: 父組件 `AirportTransfer.vue` 中的驗證邏輯把 `'--'` 當作無效值處理

**修復位置**:
- `src/components/quota/AirportTransfer.vue` 第722行
- `src/components/hongyun/AirportTransfer.vue` 第722行

**修復內容**:
```javascript
// 修改前
if (!flightNumber.value || flightNumber.value.trim() === '' || flightNumber.value.trim() === '--') {
  showToast('請輸入有效的航班編號');
  return;
}

// 修改後
if (!flightNumber.value || flightNumber.value.trim() === '') {
  showToast('請輸入有效的航班編號');
  return;
}
```

### 問題 2: 紅色錯誤文字持續顯示
**根本原因**: 彈窗打開時沒有清除之前的錯誤狀態

**修復位置**:
- `src/components/shared/ReservationInfoPopup.vue` 第267行

**修復內容**:
```javascript
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 清除所有錯誤狀態
    clearAllErrors();
    
    // ... 其他邏輯
  }
});
```

## 🧪 測試步驟

### 測試 1: 航班「不知道」功能

#### 步驟：
1. 打開 http://127.0.0.1:5174/
2. 選擇「機場接送」標籤
3. 選擇接送類型（送機/接機/來回）
4. 選擇區域
5. 點擊「查詢價格」
6. 點擊「預約」按鈕
7. 選擇預約日期時間
8. **勾選班機編號的「不知道」選項**
9. 填寫其他必填欄位：
   - 訂車人大名
   - 連絡電話
   - 上車/下車地址
   - 付款方式
10. **點擊「提交訂單」**

#### 預期結果：
- ✅ 勾選「不知道」後，班機編號欄位禁用
- ✅ 班機編號值顯示為 `--`
- ✅ 表單可以成功提交
- ✅ 不會顯示「請輸入有效的航班編號」錯誤
- ✅ 控制台顯示 `[quota/AirportTransfer] submitReservation triggered`

### 測試 2: 紅色錯誤文字清除

#### 步驟：
1. 在機場接送表單中點擊「預約」
2. **直接點擊「提交訂單」**（不填寫任何資料）
3. 觀察所有欄位顯示紅色錯誤文字
4. **關閉彈窗**
5. **重新點擊「預約」**
6. 觀察是否還有紅色錯誤文字

#### 預期結果：
- ✅ 第一次提交時顯示紅色錯誤文字
- ✅ 重新打開彈窗時，所有紅色錯誤文字都消失
- ✅ 欄位恢復正常狀態

### 測試 3: 輸入時錯誤狀態清除

#### 步驟：
1. 觸發表單驗證錯誤（直接提交空表單）
2. 觀察紅色錯誤文字
3. **開始在各個欄位輸入內容**
4. 觀察錯誤狀態是否立即清除

#### 預期結果：
- ✅ 輸入時紅色錯誤文字立即消失
- ✅ 所有欄位都有此行為

### 測試 4: 完整流程測試

#### 步驟：
1. 選擇機場接送服務
2. 選擇送機/接機類型
3. 選擇區域並查詢價格
4. 點擊預約
5. 勾選航班「不知道」
6. 填寫完整資訊
7. 提交表單

#### 預期結果：
- ✅ 整個流程順暢
- ✅ 航班「不知道」功能正常
- ✅ 表單成功提交

## 🔍 除錯方法

### 如果航班「不知道」仍無法提交：

1. **檢查控制台日誌**：
   - 應該看到 `[quota/AirportTransfer] submitReservation triggered`
   - 不應該看到「請輸入有效的航班編號」

2. **檢查 flightNumber 值**：
   ```javascript
   // 在瀏覽器控制台執行
   console.log('flightNumber:', flightNumber.value);
   // 應該顯示 '--'
   ```

3. **檢查驗證邏輯**：
   - 確認父組件的驗證邏輯已更新
   - 確認不會把 `'--'` 當作無效值

### 如果紅色錯誤文字仍然顯示：

1. **檢查 fieldErrors 狀態**：
   ```javascript
   // 在 Vue DevTools 中檢查
   fieldErrors: {
     appointmentDate: false,
     flightNumber: false,
     passengerName: false,
     contactPhone: false,
     pickupAddress: false,
     payType: false
   }
   ```

2. **檢查彈窗打開事件**：
   - 確認 `clearAllErrors()` 被調用
   - 確認在彈窗打開時執行

## ✅ 驗證清單

### 航班「不知道」功能
- [ ] 勾選後欄位禁用
- [ ] 值設為 `--`
- [ ] 可以成功提交表單
- [ ] 不顯示航班編號錯誤

### 錯誤狀態管理
- [ ] 彈窗打開時清除所有錯誤
- [ ] 輸入時清除對應欄位錯誤
- [ ] 選擇時清除對應欄位錯誤

### 回歸測試
- [ ] 原有驗證功能正常
- [ ] 其他服務類型正常
- [ ] 無新增 JavaScript 錯誤

## 🎯 成功標準

當以下條件都滿足時，Bug 修復成功：

1. ✅ 勾選航班「不知道」後可以成功提交表單
2. ✅ 重新打開彈窗時不會顯示之前的錯誤文字
3. ✅ 輸入內容時錯誤狀態立即清除
4. ✅ 所有原有功能正常工作

---

**測試環境**: http://127.0.0.1:5174/  
**測試重點**: 機場接送表單的航班編號功能  
**預期結果**: 兩個 Bug 都完全修復
