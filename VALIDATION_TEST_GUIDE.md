# 表單驗證測試指南

## 問題修復說明

### 原始問題
- 錯誤：`Toast.fail is not a function`
- 原因：在 Vant 4 中，Toast 的使用方式與項目中其他組件不一致

### 修復內容
1. **導入方式修正**：
   ```javascript
   // 修改前
   import { Toast } from 'vant';
   
   // 修改後
   import { showToast } from 'vant';
   ```

2. **調用方式修正**：
   ```javascript
   // 修改前
   Toast.fail('錯誤訊息');
   
   // 修改後
   showToast({ type: 'fail', message: '錯誤訊息' });
   ```

## 測試步驟

### 1. 啟動開發服務器
```bash
npm run dev
```
服務器會在 http://127.0.0.1:5174/ 啟動

### 2. 測試表單驗證功能

#### 測試場景 1：機場接送表單
1. 訪問 http://127.0.0.1:5174/
2. 選擇「機場接送」標籤
3. 選擇接送類型（送機/接機/來回）
4. 選擇區域
5. 點擊「查詢價格」
6. 點擊「預約」按鈕
7. **直接點擊「提交訂單」按鈕**（不填寫任何資料）

#### 預期結果：
- ✅ 顯示紅色 Toast 錯誤提示
- ✅ 對應欄位顯示紅色邊框
- ✅ 顯示具體錯誤訊息
- ✅ 表單不會提交

#### 測試場景 2：兩地接送表單
1. 選擇「兩地接送」標籤
2. 填寫起點和終點
3. 點擊「估價」
4. 選擇車型並點擊「預約」
5. **直接點擊「提交訂單」按鈕**

#### 測試場景 3：包車旅遊表單
1. 選擇「包車旅遊」標籤
2. 選擇行程和日期
3. 點擊「預約」
4. **直接點擊「提交訂單」按鈕**

### 3. 測試錯誤狀態清除功能

#### 測試步驟：
1. 觸發表單驗證錯誤（按照上述步驟）
2. 觀察欄位顯示紅色邊框
3. **開始在錯誤欄位中輸入內容**
4. 觀察紅色邊框是否自動消失

#### 預期結果：
- ✅ 當用戶開始輸入時，對應欄位的錯誤狀態會自動清除
- ✅ 紅色邊框消失
- ✅ 錯誤訊息消失

### 4. 測試特殊邏輯

#### 航班編號「不知道」功能：
1. 在機場接送表單中
2. 勾選航班編號欄位的「不知道」選項
3. 點擊「提交訂單」
4. 觀察是否不會提示航班編號錯誤

#### 電話號碼格式驗證：
1. 在連絡電話欄位輸入無效格式（如：abc123）
2. 點擊「提交訂單」
3. 觀察是否顯示「請輸入有效的連絡電話！」

## 驗證清單

### 必填欄位驗證 ✅
- [ ] 預約日期時間
- [ ] 班機編號（當需要時）
- [ ] 訂車人大名
- [ ] 連絡電話
- [ ] 上車/下車地址
- [ ] 付款方式

### 格式驗證 ✅
- [ ] 電話號碼格式（8-15位，允許數字、+、-、空格、括號）
- [ ] 空白字符處理（trim）

### 用戶體驗 ✅
- [ ] Toast 錯誤提示顯示
- [ ] 欄位紅色邊框顯示
- [ ] 具體錯誤訊息顯示
- [ ] 焦點時自動清除錯誤狀態
- [ ] 選擇時自動清除錯誤狀態

### 特殊邏輯 ✅
- [ ] 航班編號「不知道」選項
- [ ] 根據服務類型顯示不同欄位
- [ ] 地址標籤動態顯示（上車/下車）

## 常見問題排除

### 如果 Toast 仍然不顯示：
1. 檢查瀏覽器控制台是否有 JavaScript 錯誤
2. 確認 Vant 樣式是否正確載入
3. 檢查 main.js 中的 Toast 全局配置

### 如果欄位錯誤狀態不顯示：
1. 檢查 Vue DevTools 中的 fieldErrors 狀態
2. 確認 van-field 的 :error 屬性綁定
3. 檢查 CSS 樣式是否被覆蓋

### 如果驗證邏輯不正確：
1. 檢查 handleConfirm 函數的邏輯
2. 確認 props 數據綁定是否正確
3. 檢查父組件的數據傳遞

## 成功標準

當所有測試場景都通過時，表單驗證功能修復完成：
- ✅ 無 JavaScript 錯誤
- ✅ Toast 提示正常顯示
- ✅ 視覺錯誤狀態正常
- ✅ 用戶體驗流暢
- ✅ 所有驗證規則正確執行
