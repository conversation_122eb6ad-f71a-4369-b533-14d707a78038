import { createApp } from 'vue'
// import './style.css'
import App from './App.vue'
import router from './router'
import Vant from 'vant';
import {
  showToast,
  showLoadingToast,
  closeToast
} from 'vant';
import 'vant/lib/index.css';

// 创建应用实例
const app = createApp(App);

// 导出 Toast 函数，使其可以全局使用
window.showToast = showToast;
window.showLoadingToast = showLoadingToast;
window.closeToast = closeToast;

// 将 router 添加到 window 对象，方便在组件中使用
window.router = router;

// 挂载应用
app.use(router).use(Vant).mount('#app');
