# 專案概述
CARS-ORDER-V2
本專案為一個基於 Vue 3 + Vite 的訂車系統，涵蓋車輛預約、行程安排、機場接送等功能模組，並透過 Vue Router 管理單頁應用的路由。

第二版的 LINE Liff 預約/詢價表單

## 主要功能模組

- **車輛預約**：提供用戶預約車輛的功能。
- **行程安排**：管理用戶的行程安排。
- **機場接送**：提供機場接送服務。
- **配額管理**：管理車輛與服務的配額。

## 主要組件

- `CarOrder.vue`：訂車流程的主要界面或組件。
- `CarQuota.vue`：與配額或名額相關的功能。
- `Reservation.vue`：預約或預訂相關。
- `CarTour.vue`：與行程（Tour）相關的功能或界面。
- `AirportTransfer.vue`：機場接送服務。

## 技術棧

- Vue 3
- Vite
- Vue Router

## 專案結構

- `src/`：主要的前端資源目錄。
  - `App.vue`：根組件，整個應用的入口。
  - `main.js`：啟動文件。
  - `style.css`：全局樣式。
  - `assets/`：資源圖片。
  - `components/`：多個 Vue 元件，負責不同功能模組。
  - `router/index.js`：Vue Router 配置，負責前端路由管理。
- `public/`：靜態資源目錄。
- `package.json`：專案依賴與設定。
- `vite.config.js`：Vite 的配置文件。

## 總結

本專案結構清晰，依賴管理完善，整體架構適合擴展與維護。
