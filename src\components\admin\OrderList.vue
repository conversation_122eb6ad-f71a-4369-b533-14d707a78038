<template>
  <div>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="沒有更多了"
      @load="onLoad"
    >
      <van-card
        v-for="order in list"
        :key="order.id"
        :title="`訂單編號: ${order.order_number}`"
        :desc="`乘客: ${order.passenger_name}`"
        :price="order.total_amount"
        currency="TWD "
      >
        <template #tags>
          <van-tag plain type="primary">{{ order.service_type }}</van-tag>
          <van-tag plain :type="order.status === 'confirmed' ? 'success' : 'default'">
            {{ order.status }}
          </van-tag>
        </template>
        <template #footer>
          <span>預約時間: {{ new Date(order.reservation_time).toLocaleString() }}</span>
        </template>
      </van-card>
    </van-list>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { List as VanList, Card as VanCard, Tag as VanTag, Notify } from 'vant';

const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const onLoad = async () => {
  if (loading.value || finished.value) return;

  loading.value = true;
  try {
    // 這裡的 API URL 應根據您的後端服務進行調整
    const response = await axios.get('/api/vendor/2/orders');
    
    // 假設 API 回傳的資料結構是 { data: [...] }
    if (response.data && response.data.length > 0) {
      list.value.push(...response.data);
    } else {
      // 如果沒有更多資料，設定 finished 為 true
      finished.value = true;
    }
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    Notify({ type: 'danger', message: '讀取訂單失敗' });
    finished.value = true; // 發生錯誤時也停止加載
  } finally {
    loading.value = false;
  }
};

// 組件掛載時可以先觸發一次載入
onMounted(() => {
  // onLoad(); // van-list 會自動觸發第一次 onLoad
});
</script>

<style scoped>
.van-card {
  margin-bottom: 10px;
  background-color: #fff;
}
</style>